container:
  image: python

lint_task:
  setup_script:
    - pip install flake8 codespell
  script:
    - flake8 --version
    - flake8 --ignore=E203,E501,W503 --exclude=basicswap/contrib,basicswap/interface/contrib,.eggs,.tox,bin/install_certifi.py
    - codespell --check-filenames --disable-colors --quiet-level=7 --ignore-words=tests/lint/spelling.ignore-words.txt -S .git,.eggs,.tox,pgp,*.pyc,*basicswap/contrib,*basicswap/interface/contrib,*mnemonics.py,bin/install_certifi.py,*basicswap/static

test_task:
  environment:
    - TEST_RELOAD_PATH: $HOME/test_basicswap1
    - TEST_DIR: $HOME/test_basicswap2
    - BIN_DIR: /tmp/cached_bin
    - PARTICL_BINDIR: ${BIN_DIR}/particl
    - BITCOIN_BINDIR: ${BIN_DIR}/bitcoin
    - BITCOINCASH_BINDIR: ${BIN_DIR}/bitcoincash
    - LITECOIN_BINDIR: ${BIN_DIR}/litecoin
    - XMR_BINDIR: ${BIN_DIR}/monero
  setup_script:
    - apt-get update
    - apt-get install -y python3-pip pkg-config
    - pip install tox pytest
    - pip install .
  bins_cache:
    folder: /tmp/cached_bin
    reupload_on_changes: false
    fingerprint_script:
      - basicswap-prepare -v
    populate_script:
      - basicswap-prepare --bindir=/tmp/cached_bin --preparebinonly --withcoins=particl,bitcoin,bitcoincash,litecoin,monero
  script:
    - cd "${CIRRUS_WORKING_DIR}"
    - export DATADIRS="${TEST_DIR}"
    - mkdir -p "${DATADIRS}/bin"
    - cp -r ${BIN_DIR} "${DATADIRS}/bin"
    - mkdir -p "${TEST_RELOAD_PATH}/bin"
    - cp -r ${BIN_DIR} "${TEST_RELOAD_PATH}/bin"
    - # tox
    - pytest tests/basicswap/test_other.py
    - pytest tests/basicswap/test_run.py
    - pytest tests/basicswap/test_reload.py
    - pytest tests/basicswap/test_btc_xmr.py -k 'test_01_a or test_01_b or test_02_a or test_02_b'
