services:

    swapclient:
        image: i_swapclient
        container_name: swapclient
        stop_grace_period: 5m
        depends_on:
            - tor
        build:
            context: ../
        volumes:
            - ${COINDATA_PATH}:/coindata
        ports:
            - "${HTML_PORT}"  # Expose only to localhost, see .env
            - "${WS_PORT}"    # Expose only to localhost, see .env
        environment:
            - TZ
            - TOR_PROXY_HOST
        logging:
            driver: "json-file"
            options:
                max-size: "10m"
                max-file: "5"
        networks:
          - tor_net

    tor:
        image: i_tor
        container_name: tor
        build:
            context: ./tor
        volumes:
            - ${COINDATA_PATH}/tor/data:/var/lib/tor/
            - ${COINDATA_PATH}/tor/torrc:/etc/tor/torrc
        logging:
            driver: "json-file"
            options:
                max-size: "10m"
                max-file: "5"
        networks:
            tor_net:
                ipv4_address: **************

volumes:
    coindata:
        driver: local

networks:
    tor_net:
        ipam:
            driver: default
            config:
                - subnet: "************/24"
