
networks:
    default:
        name: coinswap_network
services:
    particl_core:
        image: i_particl
        build:
            context: particl
            dockerfile: Dockerfile
        container_name: particl_core
        volumes:
            - ${DATA_PATH}/particl:/data
        expose:
            - ${PART_RPC_PORT}
            - ${PART_ZMQ_PORT}
        logging:
            driver: "json-file"
            options:
                max-size: "10m"
                max-file: "3"
        restart: unless-stopped
