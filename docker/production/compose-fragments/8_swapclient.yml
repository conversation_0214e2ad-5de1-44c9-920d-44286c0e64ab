    swapclient:
        image: i_swapclient
        build:
            context: swapclient
            dockerfile: Dockerfile
        container_name: swapclient
        volumes:
            - ${DATA_PATH}/swapclient:/data
        expose:
            - "${HTML_PORT}"
            - "${WS_PORT}"
        ports:
            - "127.0.0.1:${HTML_PORT}:${HTML_PORT}"  # Expose only to localhost
            - "127.0.0.1:${WS_PORT}:${WS_PORT}"      # Expose only to localhost
        environment:
            - TZ
        logging:
            driver: "json-file"
            options:
                max-size: "10m"
                max-file: "5"
        depends_on:
            - particl_core
        restart: unless-stopped
