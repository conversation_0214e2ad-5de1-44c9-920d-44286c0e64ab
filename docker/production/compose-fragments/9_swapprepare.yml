    swapprepare:
        image: i_swapclient
        build:
            context: swapclient
            dockerfile: Dockerfile
        container_name: swapprepare
        volumes:
            - ${DATA_PATH}/swapclient:/data/swapclient
            - ${DATA_PATH}/monero_daemon:/data/monero_daemon
            - ${DATA_PATH}/monero_wallet:/data/monero_wallet
            - ${DATA_PATH}/wownero_daemon:/data/wownero_daemon
            - ${DATA_PATH}/wownero_wallet:/data/wownero_wallet
            - ${DATA_PATH}/particl:/data/particl
            - ${DATA_PATH}/bitcoin:/data/bitcoin
            - ${DATA_PATH}/litecoin:/data/litecoin
            - ${DATA_PATH}/pivx:/data/pivx
            - ${DATA_PATH}/dash:/data/dash
            - ${DATA_PATH}/firo:/data/firo
            - ${DATA_PATH}/bitcoincash:/data/bitcoincash
        environment:
            - TZ
            - BSX_DOCKER_MODE
            - UI_HTML_PORT
            - COINS_RPCBIND_IP
            - BASICSWAP_DATADIR
            - PART_DATA_DIR
            - PART_RPC_HOST
            - PART_ZMQ_PORT
            - PART_RPC_USER
            - PART_RPC_PWD
            - PART_RPC_PORT
            - BTC_DATA_DIR
            - BTC_RPC_HOST
            - BTC_RPC_PORT
            - BTC_RPC_USER
            - BTC_RPC_PWD
            - LTC_DATA_DIR
            - LTC_RPC_HOST
            - LTC_RPC_PORT
            - LTC_RPC_USER
            - LTC_RPC_PWD
            - XMR_DATA_DIR
            - XMR_RPC_HOST
            - XMR_RPC_PORT
            - XMR_ZMQ_PORT
            - XMR_WALLETS_DIR
            - XMR_WALLET_RPC_HOST
            - XMR_WALLET_RPC_PORT
            - XMR_WALLET_RPC_USER
            - XMR_WALLET_RPC_PWD
            - DEFAULT_XMR_RESTORE_HEIGHT
            - WOW_DATA_DIR
            - WOW_RPC_HOST
            - WOW_RPC_PORT
            - WOW_ZMQ_PORT
            - WOW_WALLETS_DIR
            - WOW_WALLET_RPC_HOST
            - WOW_WALLET_RPC_PORT
            - WOW_WALLET_RPC_USER
            - WOW_WALLET_RPC_PWD
            - DEFAULT_WOW_RESTORE_HEIGHT
            - PIVX_DATA_DIR
            - PIVX_RPC_HOST
            - PIVX_RPC_PORT
            - PIVX_RPC_USER
            - PIVX_RPC_PWD
            - DASH_DATA_DIR
            - DASH_RPC_HOST
            - DASH_RPC_PORT
            - DASH_RPC_USER
            - DASH_RPC_PWD
            - FIRO_DATA_DIR
            - FIRO_RPC_HOST
            - FIRO_RPC_PORT
            - FIRO_RPC_USER
            - FIRO_RPC_PWD
            - BCH_DATA_DIR
            - BCH_RPC_HOST
            - BCH_RPC_PORT
            - BCH_RPC_USER
            - BCH_RPC_PWD
        restart: "no"
