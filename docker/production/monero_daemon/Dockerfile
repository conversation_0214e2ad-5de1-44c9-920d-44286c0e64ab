FROM i_swapclient as install_stage

RUN basicswap-prepare --preparebinonly --bindir=/coin_bin --withcoin=monero --withoutcoins=particl

FROM debian:bullseye-slim

COPY --from=install_stage /coin_bin .

ENV MONERO_DATA /data

RUN groupadd -r monero && useradd -r -m -g monero monero \
    && apt-get update \
    && apt-get install -qq --no-install-recommends gosu \
    && rm -rf /var/lib/apt/lists/* \
    && mkdir -p "$MONERO_DATA" \
    && chown -R monero:monero "$MONERO_DATA" \
    && ln -sfn "$MONERO_DATA" /home/<USER>/.monero \
    && chown -h monero:monero /home/<USER>/.monero
VOLUME $MONERO_DATA

COPY entrypoint.sh /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]

CMD ["/monero/monerod", "--non-interactive", "--config-file=/home/<USER>/.monero/monerod.conf", "--confirm-external-bind"]
