xu {
    hscale="1.3", wordwraparcs=on;

    CB [label=" ", linecolor="transparent"],
    N [label="Network", linecolor="#008800", textbgcolor="#CCFFCC", arclinecolor="#008800"],
    O [label="Offerer", linecolor="#FF0000", textbgcolor="#FFCCCC", arclinecolor="#FF0000"],
    B [label="Bidder", linecolor="#0000FF", textbgcolor="#CCCCFF", arclinecolor="#0000FF"],
    C [label=" ", linecolor="transparent"], C2 [label=" ", linecolor="transparent"];
    O =>> N [label="Sends Offer"];
    N >> B [label="Detects Offer"];
    B =>> O [label="Sends Bid"];
    O abox O [label="Bid Receiving"];
    O abox O [label="Bid Received"];
    O box O [label="User accepts bid"];
    O =>> B [label="Sends BidAccept message"],
            C note C2
                [label="The BidAccept message contains the pubkeys the offerer will use and a DLEAG proof one key will work across both chains of the swapping coins",
                 textbgcolor="#FFFFCC"];
    O abox O [label="Bid Accepted"];
    B =>> O [label="Sends XmrBidLockTxSigsMessage"],
            C note C2
                [label="The XmrBidLockTxSigsMessage contains the bidder's signatures for the script-coin-lock-refund and script-coin-lock-refund-spend txns.",
                 textbgcolor="#FFFFCC"];
    O abox O [label="Exchanged script lock tx sigs msg"];
    O =>> N [label="Sends script-coin-lock-tx"];
    O abox O [label="Bid Script coin spend tx valid"];
    O =>> B [label="Sends XmrBidLockSpendTxMessage"],
            C note C2
                [label="The XmrBidLockSpendTxMessage contains the script-coin-lock-tx and proof the offerer can sign it.",
                 textbgcolor="#FFFFCC"];
    O abox O [label="Exchanged script lock spend tx msg"];

    |||;
    B => B [label="Wait for script-coin-lock-tx to confirm"], O => O [label="Wait for script-coin-lock-tx to confirm"];
    O abox O [label="Bid Script coin locked"];
    CB alt C [label="success path"] {
        B =>> N [label="Sends noscript-coin-lock-tx"];
        |||;
        O => O [label="Wait for noscript-coin-lock-tx to confirm"];
        O abox O [label="Bid Scriptless coin locked"];
        O => B [label="Sends script-coin-lock-tx release message"],
                C note C2
                    [label="The XmrBidLockReleaseMessage contains the offerer's OTVES for the script-coin-lock-tx.
                            The bidder decodes the offerer's signature from the OTVES.
                            When the offerer has the plaintext signature, they can decode the bidder's key for the noscript-lock-tx.",
                     textbgcolor="#FFFFCC"];
        O abox O [label="Bid Script coin lock released"];
        B =>> N [label="Sends script-coin-lock-spend-tx"];
        N >> O [label="Detects script-coin-lock-spend-tx"];
        O abox O [label="Bid Script tx redeemed"],
                C note C2
                    [label="The offerer extracts the bidder's plaintext signature and derives the bidder's noscript-lock-tx keyhalf.",
                     textbgcolor="#FFFFCC"];
        O =>> N [label="Sends noscript-coin-lock-spend-tx"];
        O abox O [label="Bid Scriptless tx redeemed"];
        |||;
        O => O [label="Wait for noscript-coin-lock-spend-tx to confirm"];
        O abox O [label="Bid Completed"];
    --- [label="fail path"];
        |||;
        O => O [label="Wait for script-coin-lock-tx locktime to expire"];
        O =>> N [label="Sends script-coin-lock-pre-refund-tx"],
                C note C2
                    [label="tx can be sent by either party.",
                     textbgcolor="#FFFFCC"];
        N >> O [label="script-coin-lock-pre-refund-tx"];
        O abox O [label="Bid Script pre-refund tx in chain"];
        |||;
        O => O [label="Wait for pre-refund tx to confirm"];
        O =>> N [label="Sends script-coin-lock-pre-refund-spend-tx"],
                C note C2
                    [label="Refunds the script lock tx, with the offerer's cleartext signature the bidder can refund the noscript lock tx.",
                     textbgcolor="#FFFFCC"];
        O abox O [label="Bid Failed, refunded"];
    };
}
