xu {
    hscale="1.3", wordwraparcs=on;

    CB [label=" ", linecolor="transparent"],
    N [label="Network", linecolor="#008800", textbgcolor="#CCFFCC", arclinecolor="#008800"],
    O [label="Offerer", linecolor="#FF0000", textbgcolor="#FFCCCC", arclinecolor="#FF0000"],
    B [label="Bidder", linecolor="#0000FF", textbgcolor="#CCCCFF", arclinecolor="#0000FF"],
    C [label=" ", linecolor="transparent"], C2 [label=" ", linecolor="transparent"];
    O =>> N [label="Sends Offer"];
    N >> B [label="Detects Offer"];
    B =>> O [label="Sends BidIntent message"];
    B abox B [label="Bid Request sent"];
    O box O [label="User accepts bid"];
    O =>> B [label="Sends BidIntentAccept message"],
            C note C2
                [label="The BidAccept message contains the pubkeys the offerer will use and a DLEAG proof one key will work across both chains of the swapping coins",
                 textbgcolor="#FFFFCC"];
    B abox B [label="Bid Receiving accept"];
    B =>> O [label="Sends BidAccept message"];
    B abox B [label="Bid Accepted"];

    O =>> B [label="Sends XmrBidLockTxSigsMessage"],
            C note C2
                [label="The XmrBidLockTxSigsMessage contains the offerer's signatures for the script-coin-lock-refund and script-coin-lock-refund-spend txns.",
                 textbgcolor="#FFFFCC"];
    B abox B [label="Exchanged script lock tx sigs msg"];
    B =>> N [label="Sends script-coin-lock-tx"];
    B abox B [label="Bid Script coin spend tx valid"];
    B =>> O [label="Sends XmrBidLockSpendTxMessage"],
            C note C2
                [label="The XmrBidLockSpendTxMessage contains the script-coin-lock-tx and proof the bidder can sign it.",
                 textbgcolor="#FFFFCC"];
    B abox B [label="Exchanged script lock spend tx msg"];

    |||;
    O => O [label="Wait for script-coin-lock-tx to confirm"], B => B [label="Wait for script-coin-lock-tx to confirm"];
    B abox B [label="Bid Script coin locked"];
    CB alt C [label="success path"] {
        O =>> N [label="Sends noscript-coin-lock-tx"];
        |||;
        B => B [label="Wait for noscript-coin-lock-tx to confirm"];
        B abox B [label="Bid Scriptless coin locked"];
        B => O [label="Sends script-coin-lock-tx release message"],
                C note C2
                    [label="The XmrBidLockReleaseMessage contains the bidder's OTVES for the script-coin-lock-tx.
                            The offerer decodes the bidder's signature from the OTVES.
                            When the bidder has the plaintext signature, they can decode the offerer's key for the noscript-lock-tx.",
                     textbgcolor="#FFFFCC"];
        B abox B [label="Bid Script coin lock released"];
        O =>> N [label="Sends script-coin-lock-spend-tx"];
        N >> B [label="Detects script-coin-lock-spend-tx"];
        B abox B [label="Bid Script tx redeemed"],
                C note C2
                    [label="The bidder extracts the offerer's plaintext signature and derives the offerer's noscript-lock-tx keyhalf.",
                     textbgcolor="#FFFFCC"];
        B =>> N [label="Sends noscript-coin-lock-spend-tx"];
        B abox B [label="Bid Scriptless tx redeemed"];
        |||;
        B => B [label="Wait for noscript-coin-lock-spend-tx to confirm"];
        B abox B [label="Bid Completed"];
    --- [label="fail path"];
        |||;
        B => B [label="Wait for script-coin-lock-tx locktime to expire"];
        B =>> N [label="Sends script-coin-lock-pre-refund-tx"],
                C note C2
                    [label="tx can be sent by either party.",
                     textbgcolor="#FFFFCC"];
        N >> B [label="script-coin-lock-pre-refund-tx"];
        B abox B [label="Bid Script pre-refund tx in chain"];
        |||;
        B => B [label="Wait for pre-refund tx to confirm"];
        B =>> N [label="Sends script-coin-lock-pre-refund-spend-tx"],
                C note C2
                    [label="Refunds the script lock tx, with the bidder's cleartext signature the offerer can refund the noscript lock tx.",
                     textbgcolor="#FFFFCC"];
        B abox B [label="Bid Failed, refunded"];
    };
}

