xu {
    hscale="1.3", wordwraparcs=on;

    CB [label=" ", linecolor="transparent"],
    N [label="Network", linecolor="#008800", textbgcolor="#CCFFCC", arclinecolor="#008800"],
    O [label="Offerer", linecolor="#FF0000", textbgcolor="#FFCCCC", arclinecolor="#FF0000"],
    B [label="Bidder", linecolor="#0000FF", textbgcolor="#CCCCFF", arclinecolor="#0000FF"],
    C [label=" ", linecolor="transparent"], C2 [label=" ", linecolor="transparent"];
    O =>> N [label="Sends Offer"];
    N >> B [label="Detects Offer"];
    B =>> O [label="Sends Bid"];
    B abox B [label="Bid Sent"];
    O box O [label="User accepts bid"];
    O =>> B [label="Sends BidAccept message"],
            C note C2
                [label="The BidAccept message contains the pubkeys the offerer will use and a DLEAG proof one key will work across both chains of the swapping coins",
                 textbgcolor="#FFFFCC"];
    B abox B [label="Bid Receiving accept"];
    B abox B [label="Bid Accepted"];
    B =>> O [label="Sends XmrBidLockTxSigsMessage"],
            C note C2
                [label="The XmrBidLockTxSigsMessage contains the bidder's signatures for the script-coin-lock-refund and script-coin-lock-refund-spend txns.",
                 textbgcolor="#FFFFCC"];
    B abox B [label="Exchanged script lock tx sigs msg"];
    O =>> B [label="Sends XmrBidLockSpendTxMessage"],
            C note C2
                [label="The XmrBidLockSpendTxMessage contains the script-coin-lock-tx and proof the offerer can sign it.",
                 textbgcolor="#FFFFCC"];
    O =>> N [label="Sends script-coin-lock-tx"],
    B abox B [label="Bid Script coin spend tx valid"];
    B abox B [label="Exchanged script lock spend tx msg"];
    B => B [label="Wait for script-coin-lock-tx to confirm"];
    B abox B [label="Bid Script coin locked"];
    # Bidder would only send noscript-coin-lock-tx if script-coin-lock-tx validates
    B =>> N [label="Sends noscript-coin-lock-tx"];
    B => B [label="Wait for noscript-coin-lock-tx to confirm"], O => O [label="Wait for noscript-coin-lock-tx to confirm"];
    B abox B [label="Bid Scriptless coin locked"];
    CB alt C [label="success path"] {
        O => B [label="Sends script-coin-lock-tx release message"],
                C note C2
                    [label="The XmrBidLockReleaseMessage contains the offerer's OTVES for it.
                            The bidder decodes the offerer's signature from the OTVES.
                            When the offerer has the plaintext signature, they can decode the bidder's noscript-coin-lock-tx signature.",
                     textbgcolor="#FFFFCC"];
        B abox B [label="Script coin lock released"];
        B =>> N [label="Sends script-coin-lock-spend-tx"];
        B abox B [label="Script tx redeemed"];
        B abox B [label="Bid Completed"];
    --- [label="fail path"];
        |||;
        B => B [label="Wait for script-coin-lock-tx lock to expire"];
        O =>> N [label="Sends script-coin-lock-pre-refund-tx"],
                C note C2
                    [label="tx can be sent by either party.",
                     textbgcolor="#FFFFCC"];
        N >> O [label="script-coin-lock-pre-refund-tx"];
        B abox B [label="Bid Script pre-refund tx in chain"];
        CB alt C [label="offerer refunds script coin lock tx"] {
            |||;
            O => O [label="Wait for pre-refund tx to confirm"];
            O =>> N [label="Sends script-coin-lock-pre-refund-spend-tx"],
                    C note C2
                        [label="Refunds the script lock tx, with the offerer's cleartext signature the bidder can refund the noscript lock tx.
                        Once the lock expires the pre-refund tx can be spent by the bidder.",
                         textbgcolor="#FFFFCC"];
            O abox O [label="Bid Failed, refunded"];
            N >> B [label="Detects script-coin-lock-pre-refund-spend-tx"],
                    C note C2
                        [label="Bidder recovers the offerer's scriptless chain key-shard.",
                         textbgcolor="#FFFFCC"];
            B =>> N [label="Sends scriptless-coin-lock-recover-tx"];
            B abox B [label="Bid Scriptless tx recovered"];
            B abox B [label="Bid Failed, refunded"];
        --- [label="bidder swipes script coin lock tx"];
            |||;
            B => B [label="Wait for pre-refund tx lock to expire"];
            B =>> N [label="Sends script-coin-lock-pre-refund-swipe-tx"];
            B abox B [label="Bid Failed, swiped"];
        };
    };
}
