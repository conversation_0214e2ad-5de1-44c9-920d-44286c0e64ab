<svg xmlns="http://www.w3.org/2000/svg" id="mscgenjsreplaceme" width="1264" height="1933.25" class="mscgenjsreplaceme" style="font-family:Helvetica,sans-serif;font-size:12px;font-weight:400;font-style:normal;text-decoration:none;background-color:#fff;stroke:#000;stroke-width:2" version="1.1">
    <defs>
        <marker id="mscgenjsreplacemecallback-#0000FF" class="arrow-marker" markerHeight="10" markerUnits="strokeWidth" markerWidth="10" orient="auto" refX="9" refY="3" viewBox="0 0 10 10">
            <path d="m1 1 8 2-8 2" class="arrow-style" style="stroke-dasharray:100,1;stroke:#00f"/>
        </marker>
        <marker id="mscgenjsreplacemecallback-l-#0000FF" class="arrow-marker" markerHeight="10" markerUnits="strokeWidth" markerWidth="10" orient="auto" refX="9" refY="3" viewBox="0 0 10 10">
            <path d="M17 1 9 3l8 2" class="arrow-style" style="stroke-dasharray:100,1;stroke:#00f"/>
        </marker>
        <marker id="mscgenjsreplacemecallback-#008800" class="arrow-marker" markerHeight="10" markerUnits="strokeWidth" markerWidth="10" orient="auto" refX="9" refY="3" viewBox="0 0 10 10">
            <path d="m1 1 8 2-8 2" class="arrow-style" style="stroke-dasharray:100,1;stroke:#080"/>
        </marker>
        <marker id="mscgenjsreplacemecallback-l-#008800" class="arrow-marker" markerHeight="10" markerUnits="strokeWidth" markerWidth="10" orient="auto" refX="9" refY="3" viewBox="0 0 10 10">
            <path d="M17 1 9 3l8 2" class="arrow-style" style="stroke-dasharray:100,1;stroke:#080"/>
        </marker>
        <marker id="mscgenjsreplacemecallback-#FF0000" class="arrow-marker" markerHeight="10" markerUnits="strokeWidth" markerWidth="10" orient="auto" refX="9" refY="3" viewBox="0 0 10 10">
            <path d="m1 1 8 2-8 2" class="arrow-style" style="stroke-dasharray:100,1;stroke:red"/>
        </marker>
        <marker id="mscgenjsreplacemecallback-l-#FF0000" class="arrow-marker" markerHeight="10" markerUnits="strokeWidth" markerWidth="10" orient="auto" refX="9" refY="3" viewBox="0 0 10 10">
            <path d="M17 1 9 3l8 2" class="arrow-style" style="stroke-dasharray:100,1;stroke:red"/>
        </marker>
        <marker id="mscgenjsreplacememethod-#0000FF" class="arrow-marker" markerHeight="10" markerUnits="strokeWidth" markerWidth="10" orient="auto" refX="9" refY="3" viewBox="0 0 10 10">
            <path fill="#00F" stroke="#00F" d="m1 1 8 2-8 2z" class="arrow-style"/>
        </marker>
        <marker id="mscgenjsreplacememethod-l-#0000FF" class="arrow-marker" markerHeight="10" markerUnits="strokeWidth" markerWidth="10" orient="auto" refX="9" refY="3" viewBox="0 0 10 10">
            <path fill="#00F" stroke="#00F" d="M17 1 9 3l8 2z" class="arrow-style"/>
        </marker>
        <marker id="mscgenjsreplacememethod-#FF0000" class="arrow-marker" markerHeight="10" markerUnits="strokeWidth" markerWidth="10" orient="auto" refX="9" refY="3" viewBox="0 0 10 10">
            <path fill="red" stroke="red" d="m1 1 8 2-8 2z" class="arrow-style"/>
        </marker>
        <marker id="mscgenjsreplacememethod-l-#FF0000" class="arrow-marker" markerHeight="10" markerUnits="strokeWidth" markerWidth="10" orient="auto" refX="9" refY="3" viewBox="0 0 10 10">
            <path fill="red" stroke="red" d="M17 1 9 3l8 2z" class="arrow-style"/>
        </marker>
        <style>
            .mscgenjsreplaceme path,.mscgenjsreplaceme rect{fill:none}.mscgenjsreplaceme .label-text-background{fill:#fff;stroke:#fff;stroke-width:0}.mscgenjsreplaceme .return{stroke-dasharray:5,3}.mscgenjsreplaceme text{color:inherit;stroke:none;text-anchor:middle}.mscgenjsreplaceme text.anchor-start{text-anchor:start}.mscgenjsreplaceme .arrow-marker{overflow:visible}.mscgenjsreplaceme .arrow-style{stroke-width:1}.mscgenjsreplaceme .arcrow{stroke-linecap:butt}.mscgenjsreplaceme .box,.mscgenjsreplaceme .entity{fill:#fff;stroke-linejoin:round}
        </style>
    </defs>
    <g id="mscgenjsreplaceme_body" transform="translate(47 3)">
        <path id="mscgenjsreplaceme_background" d="M-47-3h1264v1933.25H-47z" class="bglayer" style="fill:#fff;stroke:#fff;stroke-width:0"/>
        <path id="mscgenjsreplaceme_arcspans" d="M-39 778.05h1040v1130.2H-39z" class="box inline_expression alt"/>
        <g id="mscgenjsreplaceme_lifelines">
            <path d="M65 38v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 38v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 38v38" class="arcrow" style="stroke:red"/>
            <path d="M689 38v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 38v38M1105 38v38M65 76v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 76v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 76v38" class="arcrow" style="stroke:red"/>
            <path d="M689 76v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 76v38M1105 76v38M65 114v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 114v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 114v38" class="arcrow" style="stroke:red"/>
            <path d="M689 114v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 114v38M1105 114v38M65 152v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 152v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 152v38" class="arcrow" style="stroke:red"/>
            <path d="M689 152v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 152v38M1105 152v38M65 190v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 190v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 190v38" class="arcrow" style="stroke:red"/>
            <path d="M689 190v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 190v38M1105 190v38M65 228v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 228v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 228v38" class="arcrow" style="stroke:red"/>
            <path d="M689 228v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 228v38M1105 228v38M65 266v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 266v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 266v38" class="arcrow" style="stroke:red"/>
            <path d="M689 266v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 266v38M1105 266v38M65 304v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 304v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 304v38" class="arcrow" style="stroke:red"/>
            <path d="M689 304v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 304v38M1105 304v38M65 342v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 342v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 342v38" class="arcrow" style="stroke:red"/>
            <path d="M689 342v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 342v38M1105 342v38M65 380v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 380v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 380v38" class="arcrow" style="stroke:red"/>
            <path d="M689 380v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 380v38M1105 380v38M65 418v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 418v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 418v38" class="arcrow" style="stroke:red"/>
            <path d="M689 418v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 418v38M1105 418v38M65 456v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 456v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 456v38" class="arcrow" style="stroke:red"/>
            <path d="M689 456v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 456v38M1105 456v38M65 494v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 494v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 494v38" class="arcrow" style="stroke:red"/>
            <path d="M689 494v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 494v38M1105 494v38M65 532v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 532v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 532v38" class="arcrow" style="stroke:red"/>
            <path d="M689 532v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 532v38M1105 532v38M65 570v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 570v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 570v38" class="arcrow" style="stroke:red"/>
            <path d="M689 570v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 570v38M1105 570v38M65 608v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 608v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 608v38" class="arcrow" style="stroke:red"/>
            <path d="M689 608v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 608v38M1105 608v38M65 646v75.05" class="arcrow" style="stroke:transparent"/>
            <path d="M273 646v75.05" class="arcrow" style="stroke:#080"/>
            <path d="M481 646v75.05" class="arcrow" style="stroke:red"/>
            <path d="M689 646v75.05" class="arcrow" style="stroke:#00f"/>
            <path d="M897 646v75.05M1105 646v75.05M65 721.05v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 721.05v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 721.05v38" class="arcrow" style="stroke:red"/>
            <path d="M689 721.05v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 721.05v38M1105 721.05v38M65 759.05v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 759.05v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 759.05v38" class="arcrow" style="stroke:red"/>
            <path d="M689 759.05v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 759.05v38M1105 759.05v38M65 797.05v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 797.05v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 797.05v38" class="arcrow" style="stroke:red"/>
            <path d="M689 797.05v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 797.05v38M1105 797.05v38M65 835.05v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 835.05v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 835.05v38" class="arcrow" style="stroke:red"/>
            <path d="M689 835.05v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 835.05v38M1105 835.05v38M65 873.05v75.05" class="arcrow" style="stroke:transparent"/>
            <path d="M273 873.05v75.05" class="arcrow" style="stroke:#080"/>
            <path d="M481 873.05v75.05" class="arcrow" style="stroke:red"/>
            <path d="M689 873.05v75.05" class="arcrow" style="stroke:#00f"/>
            <path d="M897 873.05v75.05M1105 873.05v75.05M65 948.1v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 948.1v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 948.1v38" class="arcrow" style="stroke:red"/>
            <path d="M689 948.1v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 948.1v38M1105 948.1v38M65 986.1v86" class="arcrow" style="stroke:transparent"/>
            <path d="M273 986.1v86" class="arcrow" style="stroke:#080"/>
            <path d="M481 986.1v86" class="arcrow" style="stroke:red"/>
            <path d="M689 986.1v86" class="arcrow" style="stroke:#00f"/>
            <path d="M897 986.1v86M1105 986.1v86M65 1072.1v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1072.1v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1072.1v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1072.1v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1072.1v38M1105 1072.1v38M65 1110.1v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1110.1v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1110.1v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1110.1v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1110.1v38M1105 1110.1v38M65 1148.1v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1148.1v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1148.1v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1148.1v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1148.1v38M1105 1148.1v38M65 1186.1v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1186.1v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1186.1v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1186.1v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1186.1v38M1105 1186.1v38M65 1224.1v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1224.1v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1224.1v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1224.1v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1224.1v38M1105 1224.1v38M65 1262.1v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1262.1v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1262.1v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1262.1v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1262.1v38M1105 1262.1v38M65 1300.1v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1300.1v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1300.1v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1300.1v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1300.1v38M1105 1300.1v38M65 1338.1v75.05" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1338.1v75.05" class="arcrow" style="stroke:#080"/>
            <path d="M481 1338.1v75.05" class="arcrow" style="stroke:red"/>
            <path d="M689 1338.1v75.05" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1338.1v75.05M1105 1338.1v75.05M65 1413.15v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1413.15v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1413.15v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1413.15v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1413.15v38M1105 1413.15v38M65 1451.15v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1451.15v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1451.15v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1451.15v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1451.15v38M1105 1451.15v38M65 1489.15v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1489.15v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1489.15v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1489.15v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1489.15v38M1105 1489.15v38M65 1527.15v75.05" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1527.15v75.05" class="arcrow" style="stroke:#080"/>
            <path d="M481 1527.15v75.05" class="arcrow" style="stroke:red"/>
            <path d="M689 1527.15v75.05" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1527.15v75.05M1105 1527.15v75.05M65 1602.2v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1602.2v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1602.2v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1602.2v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1602.2v38M1105 1602.2v38M65 1640.2v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1640.2v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1640.2v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1640.2v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1640.2v38M1105 1640.2v38M65 1678.2v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1678.2v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1678.2v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1678.2v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1678.2v38M1105 1678.2v38M65 1716.2v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1716.2v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1716.2v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1716.2v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1716.2v38M1105 1716.2v38M65 1754.2v59.05" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1754.2v59.05" class="arcrow" style="stroke:#080"/>
            <path d="M481 1754.2v59.05" class="arcrow" style="stroke:red"/>
            <path d="M689 1754.2v59.05" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1754.2v59.05M1105 1754.2v59.05M65 1813.25v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1813.25v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1813.25v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1813.25v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1813.25v38M1105 1813.25v38M65 1851.25v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1851.25v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1851.25v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1851.25v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1851.25v38M1105 1851.25v38M65 1889.25v38" class="arcrow" style="stroke:transparent"/>
            <path d="M273 1889.25v38" class="arcrow" style="stroke:#080"/>
            <path d="M481 1889.25v38" class="arcrow" style="stroke:red"/>
            <path d="M689 1889.25v38" class="arcrow" style="stroke:#00f"/>
            <path d="M897 1889.25v38M1105 1889.25v38" class="arcrow" style="stroke:transparent"/>
        </g>
        <g id="mscgenjsreplaceme_sequence">
            <path d="M0 0h130v38H0z" class="entity" style="stroke:transparent"/>
            <text x="65" y="22.75" class="entity-text"><tspan> </tspan></text>
            <path d="M208 0h130v38H208z" class="entity" style="fill:#cfc;stroke:#080"/>
            <text x="273" y="22.75" class="entity-text"><tspan>Network</tspan></text>
            <path d="M416 0h130v38H416z" class="entity" style="fill:#fcc;stroke:red"/>
            <text x="481" y="22.75" class="entity-text"><tspan>Offerer</tspan></text>
            <path d="M624 0h130v38H624z" class="entity" style="fill:#ccf;stroke:#00f"/>
            <text x="689" y="22.75" class="entity-text"><tspan>Bidder</tspan></text>
            <path d="M832 0h130v38H832z" class="entity" style="stroke:transparent"/>
            <text x="897" y="22.75" class="entity-text"><tspan> </tspan></text>
            <path d="M1040 0h130v38h-130z" class="entity" style="stroke:transparent"/>
            <text x="1105" y="22.75" class="entity-text"><tspan> </tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#FF0000)" d="M481 95H273" class="arc directional callback" style="stroke:red"/>
            <path d="M345.09 79.25h63.83v14h-63.83z" class="label-text-background"/>
            <text x="377" y="90.25" class="directional-text callback-text"><tspan>Sends Offer</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#008800)" d="M273 133h416" class="arc directional return" style="stroke:#080"/>
            <path d="M445.76 117.25h70.48v14h-70.48z" class="label-text-background"/>
            <text x="481" y="128.25" class="directional-text return-text"><tspan>Detects Offer</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#0000FF)" d="M689 171H481" class="arc directional callback" style="stroke:#00f"/>
            <path d="M557.65 155.25h54.7v14h-54.7z" class="label-text-background"/>
            <text x="585" y="166.25" class="directional-text callback-text"><tspan>Sends Bid</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#FF0000)" d="M481 323h208" class="arc directional callback" style="stroke:red"/>
            <path d="M513.29 307.25h143.74v14H513.29z" class="label-text-background"/>
            <text x="585" y="318.25" class="directional-text callback-text"><tspan>Sends BidAccept message</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#0000FF)" d="M689 399H481" class="arc directional callback" style="stroke:#00f"/>
            <path d="M491.29 383.25h187.74v14H491.29z" class="label-text-background"/>
            <text x="585" y="394.25" class="directional-text callback-text"><tspan>Sends XmrBidLockTxSigsMessage</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#FF0000)" d="M481 475H273" class="arc directional callback" style="stroke:red"/>
            <path d="M311.64 459.25h130.72v14H311.64z" class="label-text-background"/>
            <text x="377" y="470.25" class="directional-text callback-text"><tspan>Sends script-coin-lock-tx</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#FF0000)" d="M481 551h208" class="arc directional callback" style="stroke:red"/>
            <path d="M485.61 535.25H684.7v14H485.61z" class="label-text-background"/>
            <text x="585" y="546.25" class="directional-text callback-text"><tspan>Sends XmrBidLockSpendTxMessage</tspan></text>
            <path marker-end="url(#mscgenjsreplacememethod-#0000FF)" d="M689 675.92c104 .1 104 22.8 0 22.8" class="arc directional method" style="stroke:#00f"/>
            <path d="M692 627.67h40.91v14H692z" class="label-text-background"/>
            <text x="692" y="638.67" class="directional-text method-text anchor-start"><tspan>Wait for</tspan></text>
            <path d="M692 643.67h107.01v14H692z" class="label-text-background"/>
            <text x="692" y="654.67" class="directional-text method-text anchor-start"><tspan>script-coin-lock-tx to</tspan></text>
            <path d="M692 659.67h39.34v14H692z" class="label-text-background"/>
            <text x="692" y="670.67" class="directional-text method-text anchor-start"><tspan>confirm</tspan></text>
            <path marker-end="url(#mscgenjsreplacememethod-#FF0000)" d="M481 675.92c104 .1 104 22.8 0 22.8" class="arc directional method" style="stroke:red"/>
            <path d="M484 627.67h40.91v14H484z" class="label-text-background"/>
            <text x="484" y="638.67" class="directional-text method-text anchor-start"><tspan>Wait for</tspan></text>
            <path d="M484 643.67h107.01v14H484z" class="label-text-background"/>
            <text x="484" y="654.67" class="directional-text method-text anchor-start"><tspan>script-coin-lock-tx to</tspan></text>
            <path d="M484 659.67h39.34v14H484z" class="label-text-background"/>
            <text x="484" y="670.67" class="directional-text method-text anchor-start"><tspan>confirm</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#0000FF)" d="M689 816.05H273" class="arc directional callback" style="stroke:#00f"/>
            <path d="M408.97 800.3h144.06v14H408.97z" class="label-text-background"/>
            <text x="481" y="811.3" class="directional-text callback-text"><tspan>Sends noscript-coin-lock-tx</tspan></text>
            <path marker-end="url(#mscgenjsreplacememethod-#FF0000)" d="M481 902.97c104 .1 104 22.8 0 22.8" class="arc directional method" style="stroke:red"/>
            <path d="M484 854.72h40.91v14H484z" class="label-text-background"/>
            <text x="484" y="865.72" class="directional-text method-text anchor-start"><tspan>Wait for</tspan></text>
            <path d="M484 870.72h120.36v14H484z" class="label-text-background"/>
            <text x="484" y="881.72" class="directional-text method-text anchor-start"><tspan>noscript-coin-lock-tx to</tspan></text>
            <path d="M484 886.72h39.34v14H484z" class="label-text-background"/>
            <text x="484" y="897.72" class="directional-text method-text anchor-start"><tspan>confirm</tspan></text>
            <path marker-end="url(#mscgenjsreplacememethod-#FF0000)" d="M481 1029.1h208" class="arc directional method" style="stroke:red"/>
            <path d="M519.64 1013.35h130.72v14H519.64z" class="label-text-background"/>
            <text x="585" y="1024.35" class="directional-text method-text"><tspan>Sends script-coin-lock-tx</tspan></text>
            <path d="M539.3 1031.35h91.71v14H539.3z" class="label-text-background"/>
            <text x="585" y="1042.35" class="directional-text method-text"><tspan>release message</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#0000FF)" d="M689 1129.1H273" class="arc directional callback" style="stroke:#00f"/>
            <path d="M397.3 1113.35h167.41v14H397.3z" class="label-text-background"/>
            <text x="481" y="1124.35" class="directional-text callback-text"><tspan>Sends script-coin-lock-spend-tx</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#008800)" d="M273 1167.1h208" class="arc directional return" style="stroke:#080"/>
            <path d="M289.97 1151.35h174.06v14H289.97z" class="label-text-background"/>
            <text x="377" y="1162.35" class="directional-text return-text"><tspan>Detects script-coin-lock-spend-tx</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#FF0000)" d="M481 1243.1H273" class="arc directional callback" style="stroke:red"/>
            <path d="M286.63 1227.35h180.75v14H286.63z" class="label-text-background"/>
            <text x="377" y="1238.35" class="directional-text callback-text"><tspan>Sends noscript-coin-lock-spend-tx</tspan></text>
            <path marker-end="url(#mscgenjsreplacememethod-#FF0000)" d="M481 1368.02c104 .1 104 22.8 0 22.8" class="arc directional method" style="stroke:red"/>
            <path d="M484 1319.77h40.91v14H484z" class="label-text-background"/>
            <text x="484" y="1330.77" class="directional-text method-text anchor-start"><tspan>Wait for</tspan></text>
            <path d="M484 1335.77h143.39v14H484z" class="label-text-background"/>
            <text x="484" y="1346.77" class="directional-text method-text anchor-start"><tspan>noscript-coin-lock-spend-tx</tspan></text>
            <path d="M484 1351.77h52.69v14H484z" class="label-text-background"/>
            <text x="484" y="1362.77" class="directional-text method-text anchor-start"><tspan>to confirm</tspan></text>
            <path d="M-39 1470.15h1040" class="inline_expression_divider" style="stroke-dasharray:10,5"/>
            <path d="M459.98 1462.9h42.03v14h-42.03z" class="label-text-background"/>
            <text x="481" y="1473.9" class="empty-text comment-row-text"><tspan>fail path</tspan></text>
            <path marker-end="url(#mscgenjsreplacememethod-#FF0000)" d="M481 1557.07c104 .1 104 22.8 0 22.8" class="arc directional method" style="stroke:red"/>
            <path d="M484 1508.82h40.91v14H484z" class="label-text-background"/>
            <text x="484" y="1519.82" class="directional-text method-text anchor-start"><tspan>Wait for</tspan></text>
            <path d="M484 1524.82h93.36v14H484z" class="label-text-background"/>
            <text x="484" y="1535.82" class="directional-text method-text anchor-start"><tspan>script-coin-lock-tx</tspan></text>
            <path d="M484 1540.82h93.7v14H484z" class="label-text-background"/>
            <text x="484" y="1551.82" class="directional-text method-text anchor-start"><tspan>locktime to expire</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#FF0000)" d="M481 1621.2H273" class="arc directional callback" style="stroke:red"/>
            <path d="M359.98 1605.45h34.03v14h-34.03z" class="label-text-background"/>
            <text x="377" y="1616.45" class="directional-text callback-text"><tspan>Sends</tspan></text>
            <path d="M300.64 1623.45h152.72v14H300.64z" class="label-text-background"/>
            <text x="377" y="1634.45" class="directional-text callback-text"><tspan>script-coin-lock-pre-refund-tx</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#008800)" d="M273 1659.2h208" class="arc directional return" style="stroke:#080"/>
            <path d="M300.64 1643.45h152.72v14H300.64z" class="label-text-background"/>
            <text x="377" y="1654.45" class="directional-text return-text"><tspan>script-coin-lock-pre-refund-tx</tspan></text>
            <path marker-end="url(#mscgenjsreplacememethod-#FF0000)" d="M481 1776.12c104 .1 104 22.8 0 22.8" class="arc directional method" style="stroke:red"/>
            <path d="M484 1743.87h40.91v14H484z" class="label-text-background"/>
            <text x="484" y="1754.87" class="directional-text method-text anchor-start"><tspan>Wait for</tspan></text>
            <path d="M484 1759.87h124.06v14H484z" class="label-text-background"/>
            <text x="484" y="1770.87" class="directional-text method-text anchor-start"><tspan>pre-refund tx to confirm</tspan></text>
            <path marker-end="url(#mscgenjsreplacemecallback-#FF0000)" d="M481 1832.25H273" class="arc directional callback" style="stroke:red"/>
            <path d="M359.98 1816.5h34.03v14h-34.03z" class="label-text-background"/>
            <text x="377" y="1827.5" class="directional-text callback-text"><tspan>Sends</tspan></text>
            <path d="M282.3 1834.5h189.41v14H282.3z" class="label-text-background"/>
            <text x="377" y="1845.5" class="directional-text callback-text"><tspan>script-coin-lock-pre-refund-spend-tx</tspan></text>
        </g>
        <g id="mscgenjsreplaceme_notes">
            <path d="m381 209 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="212.75" class="box-text abox-text"><tspan>Bid Receiving</tspan></text>
            <path d="m381 247 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="250.75" class="box-text abox-text"><tspan>Bid Received</tspan></text>
            <path d="M381 268h200v34H381z" class="box" style="stroke:red"/>
            <text x="481" y="288.75" class="box-text"><tspan>User accepts bid</tspan></text>
            <path d="M797 306h399v9h9m-9-9 9 9v25H797v-34z" class="box note" style="fill:#ffc"/>
            <text x="1001" y="318.75" class="box-text note-text"><tspan>The BidAccept message contains the pubkeys the offerer will use and a</tspan></text>
            <text x="1001" y="334.75" class="box-text note-text"><tspan>DLEAG proof one key will work across both chains of the swapping coins</tspan></text>
            <path d="m381 361 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="364.75" class="box-text abox-text"><tspan>Bid Accepted</tspan></text>
            <path d="M797 382h399v9h9m-9-9 9 9v25H797v-34z" class="box note" style="fill:#ffc"/>
            <text x="1001" y="394.75" class="box-text note-text"><tspan>The XmrBidLockTxSigsMessage contains the bidder&apos;s signatures for the</tspan></text>
            <text x="1001" y="410.75" class="box-text note-text"><tspan>script-coin-lock-refund and script-coin-lock-refund-spend txns.</tspan></text>
            <path d="m381 437 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="432.75" class="box-text abox-text"><tspan>Exchanged script lock tx sigs</tspan></text>
            <text x="481" y="448.75" class="box-text abox-text"><tspan>msg</tspan></text>
            <path d="m381 513 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="516.75" class="box-text abox-text"><tspan>Bid Script coin spend tx valid</tspan></text>
            <path d="M797 534h399v9h9m-9-9 9 9v25H797v-34z" class="box note" style="fill:#ffc"/>
            <text x="1001" y="546.75" class="box-text note-text"><tspan>The XmrBidLockSpendTxMessage contains the script-coin-lock-tx and</tspan></text>
            <text x="1001" y="562.75" class="box-text note-text"><tspan>proof the offerer can sign it.</tspan></text>
            <path d="m381 589 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="584.75" class="box-text abox-text"><tspan>Exchanged script lock spend tx</tspan></text>
            <text x="481" y="600.75" class="box-text abox-text"><tspan>msg</tspan></text>
            <path d="m381 740.05 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="743.8" class="box-text abox-text"><tspan>Bid Script coin locked</tspan></text>
            <path d="M-38 778.05h98.39v11l-7 7H-38" class="box inline_expression_label"/>
            <text x="-36" y="791.3" class="inline_expression-text alt-text anchor-start"><tspan>alt: success path</tspan></text>
            <path d="m381 967.1 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="970.85" class="box-text abox-text"><tspan>Bid Scriptless coin locked</tspan></text>
            <path d="M797 988.1h399v9h9m-9-9 9 9v73H797v-82z" class="box note" style="fill:#ffc"/>
            <text x="1001" y="1000.85" class="box-text note-text"><tspan>The XmrBidLockReleaseMessage contains the offerer&apos;s OTVES for the</tspan></text>
            <text x="1001" y="1016.85" class="box-text note-text"><tspan>script-coin-lock-tx.                             The bidder decodes the</tspan></text>
            <text x="1001" y="1032.85" class="box-text note-text"><tspan>offerer&apos;s signature from the OTVES.                             When the</tspan></text>
            <text x="1001" y="1048.85" class="box-text note-text"><tspan>offerer has the plaintext signature, they can decode the bidder&apos;s key</tspan></text>
            <text x="1001" y="1064.85" class="box-text note-text"><tspan>for the noscript-lock-tx.</tspan></text>
            <path d="m381 1091.1 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="1094.85" class="box-text abox-text"><tspan>Bid Script coin lock released</tspan></text>
            <path d="m381 1205.1 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="1208.85" class="box-text abox-text"><tspan>Bid Script tx redeemed</tspan></text>
            <path d="M797 1188.1h399v9h9m-9-9 9 9v25H797v-34z" class="box note" style="fill:#ffc"/>
            <text x="1001" y="1200.85" class="box-text note-text"><tspan>The offerer extracts the bidder&apos;s plaintext signature and derives the</tspan></text>
            <text x="1001" y="1216.85" class="box-text note-text"><tspan>bidder&apos;s noscript-lock-tx keyhalf.</tspan></text>
            <path d="m381 1281.1 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="1284.85" class="box-text abox-text"><tspan>Bid Scriptless tx redeemed</tspan></text>
            <path d="m381 1432.15 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="1435.9" class="box-text abox-text"><tspan>Bid Completed</tspan></text>
            <path d="M797 1604.2h399v9h9m-9-9 9 9v25H797v-34z" class="box note" style="fill:#ffc"/>
            <text x="1001" y="1624.95" class="box-text note-text"><tspan>tx can be sent by either party.</tspan></text>
            <path d="m381 1697.2 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="1692.95" class="box-text abox-text"><tspan>Bid Script pre-refund tx in</tspan></text>
            <text x="481" y="1708.95" class="box-text abox-text"><tspan>chain</tspan></text>
            <path d="M797 1815.25h399v9h9m-9-9 9 9v25H797v-34z" class="box note" style="fill:#ffc"/>
            <text x="1001" y="1828" class="box-text note-text"><tspan>Refunds the script lock tx, with the offerer&apos;s cleartext signature</tspan></text>
            <text x="1001" y="1844" class="box-text note-text"><tspan>the bidder can refund the noscript lock tx.</tspan></text>
            <path d="m381 1870.25 3-17h194l3 17-3 17H384z" class="box abox" style="stroke:red"/>
            <text x="481" y="1874" class="box-text abox-text"><tspan>Bid Failed, refunded</tspan></text>
        </g>
    </g>
</svg>
