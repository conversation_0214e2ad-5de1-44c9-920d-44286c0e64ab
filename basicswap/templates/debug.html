{% include 'header.html' %}
{% from 'style.html' import breadcrumb_line_svg, start_process_svg %} 
<div class="container mx-auto">
 <section class="p-5 mt-5">
  <div class="flex flex-wrap items-center -m-2">
   <div class="w-full md:w-1/2 p-2">
    <ul class="flex flex-wrap items-center gap-x-3 mb-2">
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/">
       <p>Home</p>
      </a>
     </li>
      <li> {{ breadcrumb_line_svg | safe }} </li>
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/debug">Debug</a>
     </li>
      <li> {{ breadcrumb_line_svg | safe }} </li>
    </ul>
   </div>
  </div>
 </section>
 <section class="py-4">
  <div class="container px-4 mx-auto">
   <div class="relative py-11 px-16 bg-coolGray-900 dark:bg-blue-500 rounded-md overflow-hidden">
    <img class="absolute z-10 left-4 top-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute z-10 right-4 bottom-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute h-64 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 object-cover" src="/static/images/elements/wave.svg" alt="">
    <div class="relative z-20 flex flex-wrap items-center -m-3">
     <div class="w-full md:w-1/2 p-3">
      <h2 class="mb-6 text-4xl font-bold text-white tracking-tighter">Debug</h2>
      <p class="font-normal text-coolGray-200 dark:text-white">Expert debug options</p>
     </div>
    </div>
   </div>
  </div>
 </section>
 {% include 'inc_messages.html' %}
 <section>
  <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden min-height-50">
   <div class="border-coolGray-100">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="container mt-5 mx-auto">
       <div class="pt-6 pb-6 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
        <div class="px-6">
         <div class="w-full mt-6 pb-6 overflow-x-auto">
          <table class="w-full min-w-max text-sm">
           <thead class="uppercase">
            <tr class="text-left">
             <th class="p-0">
              <div class="py-3 px-6 rounded-tl-xl  bg-coolGray-200 dark:bg-gray-600">
               <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Options</span>
              </div>
             </th>
             <th class="p-0">
              <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
               <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold py-3 px-6"></span>
              </div>
             </th>
            </tr>
           </thead>
           <form method="post">
            <tr class="opacity-100 text-gray-500 dark:text-gray-100">
             <td class="py-3 px-6 bold">Remove expired offers and bids</td>
             <td td class="py-3 px-6 ">
              <button name="remove_expired" type="submit" value="Yes" class="w-60 flex flex-wrap justify-center py-2 px-4 bg-red-500 hover:bg-red-600 font-medium text-sm text-white border border-red-500 rounded-md shadow-button focus:ring-0 focus:outline-none" onclick="return confirmRemoveExpired();">
              Remove Data</button>
             </td>
            </tr>
            <tr class="opacity-100 text-gray-500 dark:text-gray-100">
             <td class="py-3 px-6 bold">Reinitialise XMR wallet</td>
             <td td class="py-3 px-6 ">
              <button name="reinit_xmr" type="submit" value="Yes" class="w-60 flex flex-wrap justify-center py-2 px-4 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
              {{ start_process_svg| safe }} Start Process</button>
             </td>
            </tr>
            <input type="hidden" name="formid" value="{{ form_id }}">
           </form>
          </table>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
 </section>
 <section>
  <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden ">
   <div class="pb-6 ">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="container mx-auto">
       <div class="pt-6 pb-6 bg-coolGray-100 border-t border-gray-100 dark:border-gray-400 dark:bg-gray-500 rounded-bl-xl rounded-br-xl">
        <div class="px-6"></div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
 </section>
 <!-- todo
{% if result %}
<textarea class="monospace" rows="40" cols="160">
{{ result }}
</textarea>
{% endif %}
-->
</div>
{% include 'footer.html' %}
</div>
</body>
<script>
function confirmRemoveExpired() {
  return confirm("This will remove all expired offers and bids from the database - Are you sure?");
}
</script>
</html>
