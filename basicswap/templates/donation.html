{% include 'header.html' %}
{% from 'style.html' import breadcrumb_line_svg, donation_svg %}
<div class="container mx-auto">
  <section class="p-5 mt-5">
    <div class="flex flex-wrap items-center -m-2">
      <div class="w-full md:w-1/2 p-2">
        <ul class="flex flex-wrap items-center gap-x-3 mb-2">
          <li>
            <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/">
              <p>Home</p>
            </a>
          </li>
          {{ breadcrumb_line_svg | safe }}
          <li>
            <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/donation">Donation</a>
          </li>
          {{ breadcrumb_line_svg | safe }}
        </ul>
      </div>
    </div>
  </section>

  <section class="py-4">
    <div class="container px-4 mx-auto">
      <div class="relative py-11">
        <div class="absolute inset-0 bg-coolGray-100 dark:bg-gray-500 rounded-lg"></div>
        <div class="relative container px-4 mx-auto">
          <div class="flex flex-wrap items-center -mx-4">
            <div class="w-full lg:w-1/2 px-4 mb-16 lg:mb-0">
              <div class="max-w-md">
                <div class="inline-flex items-center px-3 py-1 mb-6 text-xs leading-5 text-green-500 bg-green-100 font-medium uppercase rounded-full shadow-sm">
                  {{ donation_svg | safe }}
                  Support BasicSwap
                </div>
                <h2 class="mb-4 text-3xl md:text-4xl leading-tight text-coolGray-900 dark:text-white font-bold tracking-tighter">
                  Help Keep BasicSwap Free
                </h2>
                <p class="mb-6 text-lg md:text-xl text-coolGray-500 dark:text-gray-300 font-medium">
                  BasicSwap is completely free and open-source software that charges no fees for its use. The project is entirely funded by generous community donations from users who believe in decentralized, censorship-resistant trading.
                </p>
                <p class="mb-6 text-lg md:text-xl text-coolGray-500 dark:text-gray-300 font-medium">
                  Your donations are vital to keeping this project alive, accelerating development, and expanding our reach to more users who value financial freedom and privacy.
                </p>
                <div class="mb-6">
                  <h3 class="mb-3 text-xl text-coolGray-900 dark:text-white font-bold">
                    Every donation, regardless of size, directly funds:
                  </h3>
                  <ul class="text-coolGray-500 dark:text-gray-300 space-y-2">
                    <li class="flex items-start">
                      <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      Core development and new feature implementation
                    </li>
                    <li class="flex items-start">
                      <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      Security audits and testing infrastructure
                    </li>
                    <li class="flex items-start">
                      <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      Documentation and educational resources
                    </li>
                    <li class="flex items-start">
                      <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      Community outreach and adoption initiatives
                    </li>
                  </ul>
                </div>
                <p class="text-lg md:text-xl text-coolGray-500 dark:text-gray-300 font-medium">
                  Together, we're building financial tools that empower individuals and resist censorship. Thank you for being part of this movement toward true financial freedom.
                </p>
              </div>
            </div>
            <div class="w-full lg:w-1/2 px-4">
              <div class="max-w-lg mx-auto">
                <div class="bg-white dark:bg-gray-600 rounded-lg shadow-lg p-8">
                  <h3 class="mb-6 text-2xl text-coolGray-900 dark:text-white font-bold text-center">
                    Donation Addresses
                  </h3>

                  <!-- Monero -->
                  <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center mb-3">
                      <img src="/static/images/coins/Monero.png" alt="Monero" class="w-8 h-8 mr-3">
                      <h4 class="text-lg font-semibold text-coolGray-900 dark:text-white">Monero (XMR)</h4>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded border">
                      <p class="text-sm font-mono text-coolGray-700 dark:text-gray-300 break-all">
                        8BuQsYBNdfhfoWsvVR1unE7YuZEoTkC4hANaPm2fD6VR5VM2DzQoJhq2CHHXUN1UCWQfH3dctJgorSRxksVa5U4RNTJkcAc
                      </p>
                    </div>
                  </div>

                  <!-- Litecoin -->
                  <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center mb-3">
                      <img src="/static/images/coins/Litecoin.png" alt="Litecoin" class="w-8 h-8 mr-3">
                      <h4 class="text-lg font-semibold text-coolGray-900 dark:text-white">Litecoin (LTC)</h4>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded border">
                      <p class="text-sm font-mono text-coolGray-700 dark:text-gray-300 break-all">
                        ltc1qevlumv48nz2afl0re9ml4tdewc56svxq3egkyt
                      </p>
                    </div>
                  </div>

                  <!-- Litecoin MWEB -->
                  <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center mb-3">
                      <img src="/static/images/coins/Litecoin-MWEB.png" alt="Litecoin MWEB" class="w-8 h-8 mr-3">
                      <h4 class="text-lg font-semibold text-coolGray-900 dark:text-white">Litecoin MWEB</h4>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded border">
                      <p class="text-sm font-mono text-coolGray-700 dark:text-gray-300 break-all">
                        ltcmweb1qqt9rwznnxzkghv4s5wgtwxs0m0ry6n3atp95f47slppapxljde3xyqmdlnrc8ag7y2k354jzdc4pc4ks0kr43jehr77lngdecgh6689nn5mgv5yn
                      </p>
                    </div>
                  </div>

                  <!-- Bitcoin -->
                  <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center mb-3">
                      <img src="/static/images/coins/Bitcoin.png" alt="Bitcoin" class="w-8 h-8 mr-3">
                      <h4 class="text-lg font-semibold text-coolGray-900 dark:text-white">Bitcoin (BTC)</h4>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded border">
                      <p class="text-sm font-mono text-coolGray-700 dark:text-gray-300 break-all">
                        ******************************************
                      </p>
                    </div>
                  </div>

                  <!-- Particl -->
                  <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center mb-3">
                      <img src="/static/images/coins/Particl.png" alt="Particl" class="w-8 h-8 mr-3">
                      <h4 class="text-lg font-semibold text-coolGray-900 dark:text-white">Particl (PART)</h4>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded border">
                      <p class="text-sm font-mono text-coolGray-700 dark:text-gray-300 break-all">
                        pw1qf59ef0zjdckldjs8smfhv4j04gsjv302w7pdpz
                      </p>
                    </div>
                  </div>

                  <div class="text-center">
                    <p class="text-sm text-coolGray-500 dark:text-gray-400">
                      Every contribution helps make decentralized trading more accessible to everyone.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

{% include 'footer.html' %}
