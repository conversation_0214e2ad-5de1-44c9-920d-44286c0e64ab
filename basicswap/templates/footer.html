{% from 'style.html' import love_svg, github_svg %}
<section class="overflow-hidden">
  <div class="container px-4 mx-auto">
    <div class="flex flex-wrap lg:items-center pt-24 pb-12 -mx-4">
      <div class="w-full md:w-3/4 px-4">
        <a class="block mb-8 max-w-max" href="/">
          <img src="/static/images/logos/basicswap-logo.svg" class="h-8 imageshow dark-image">
          <img src="/static/images/logos/basicswap-logo-dark.svg" class="h-8 imageshow light-image">
        </a>
        <div class="mb-12 md:mb-0 flex flex-wrap -mx-3 md:-mx-6">
          <div class="w-full md:w-auto p-3 md:py-0 md:px-6"><a class="inline-block text-coolGray-500 dark:text-gray-300 font-medium" href="https://academy.particl.io/en/latest/basicswap-dex/basicswap_explained.html" target="_blank">BasicSwap Explained</a></div>
          <div class="w-full md:w-auto p-3 md:py-0 md:px-6"><a class="inline-block text-coolGray-500 dark:text-gray-300 font-medium" href="https://academy.particl.io/en/latest/basicswap-guides/basicswapguides_installation.html" target="_blank">Tutorials and Guides</a></div>
          <div class="w-full md:w-auto p-3 md:py-0 md:px-6"><a class="inline-block text-coolGray-500 dark:text-gray-300 font-medium" href="https://academy.particl.io/en/latest/faq/get_support.html" target="_blank">Get Support</a></div>
          <div class="w-full md:w-auto p-3 md:py-0 md:px-6"><a class="inline-block text-coolGray-500 dark:text-gray-300 font-medium" href="https://basicswapdex.com/terms" target="_blank">Terms and Conditions</a></div>
        </div>
      </div>
      <div class="w-full md:w-1/4 px-4"> </div>
    </div>
  </div>
  <div class="border-b border-gray-100 dark:border-gray-500 dark:bg-body dark:border-b-2"></div>
  <div class="container px-4 mx-auto">
    <div class="flex flex-wrap items-center py-12 md:pb-32">
      <div class="w-full md:w-1/2 mb-6 md:mb-0">
        <div class="flex items-center">
          <div class="flex items-center">
            <p class="text-sm text-gray-90 dark:text-white font-medium">© 2025~ (BSX) BasicSwap</p> <span class="w-1 h-1 mx-1.5 bg-gray-500 dark:bg-white rounded-full"></span>
            <p class="text-sm text-coolGray-400  font-medium">BSX: v{{ version }}</p> <span class="w-1 h-1 mx-1.5 bg-gray-500 dark:bg-white rounded-full"></span>
            <p class="text-sm text-coolGray-400  font-medium">GUI: v3.2.1</p> <span class="w-1 h-1 mx-1.5 bg-gray-500 dark:bg-white rounded-full"></span>
            <p class="mr-2 text-sm font-bold dark:text-white text-gray-90 ">Made with </p>
            {{ love_svg | safe }}
            </div>
        </div>
      </div>
      <div class="w-full md:w-1/2">
        <div class="flex flex-wrap md:justify-end -mx-5">
          <div class="px-5">
            <a class="inline-block text-coolGray-300 hover:text-coolGray-400" href="https://github.com/basicswap/basicswap" target="_blank">
            {{ github_svg | safe }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
