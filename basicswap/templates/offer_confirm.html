{% include 'header.html' %} {% from 'style.html' import breadcrumb_line_svg, input_down_arrow_offer_svg, select_network_svg, select_address_svg, select_swap_type_svg, select_bid_amount_svg, select_rate_svg, step_one_svg, step_two_svg, step_three_svg, select_setup_svg, input_time_svg, select_target_svg , select_auto_strategy_svg %}
<div class="container mx-auto">
  <section class="p-5 mt-5">
    <div class="flex flex-wrap items-center -m-2">
      <div class="w-full md:w-1/2 p-2">
        <ul class="flex flex-wrap items-center gap-x-3 mb-2">
          <li> <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/">
              <p>Home</p>
            </a> </li>
          <li>{{ breadcrumb_line_svg | safe }}</li>
          <li><a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/newoffer">Place</a></li>
          <li>{{ breadcrumb_line_svg | safe }}</li>
          <li><a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="#">Setup</a></li>
          <li>{{ breadcrumb_line_svg | safe }}</li>
          <li><a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="#">Confirm</a></li>
          <li>{{ breadcrumb_line_svg | safe }}</li>
        </ul>
      </div>
    </div>
  </section>
  <section class="py-4">
    <div class="container px-4 mx-auto">
      <div class="relative py-11 px-16 bg-coolGray-900 dark:bg-blue-500 rounded-md overflow-hidden">
        <img class="absolute z-10 left-4 top-4" src="/static/images/elements/dots-red.svg" alt="">
        <img class="absolute z-10 right-4 bottom-4" src="/static/images/elements/dots-red.svg" alt="">
        <img class="absolute h-64 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 object-cover" src="/static/images/elements/wave.svg" alt="">
        <div class="relative z-20 flex flex-wrap items-center -m-3">
          <div class="w-full md:w-1/2 p-3">
            <h2 class="mb-6 text-4xl font-bold text-white tracking-tighter">Confirm your Offer</h2>
            <p class="font-normal text-coolGray-200 dark:text-white">Place an order on the network order book.</p>
          </div>
        </div>
      </div>
    </div>
  </section>
  {% include 'inc_messages.html' %}
  <section>
    <div class="p-5 mb-5">
      <div class="mx-4 p-4">
        <div class="flex items-center">
          <div class="flex items-center text-teal-600 relative">
            <div class="rounded-full h-12 w-12 py-3 border-2 border-blue-500">
            {{ step_one_svg | safe }}
          </div>
            <div class="absolute top-0 -ml-10 text-center mt-16 w-32 text-xs font-medium uppercase dark:text-white text-gray-600">Step 1</div>
          </div>
          <div class="flex-auto border-t-2 border-blue-500 dark:border-blue-500"></div>
          <div class="flex items-center text-teal-600 relative ">
            <div class="rounded-full transition duration-500 ease-in-out h-12 w-12 py-3 border-2 border-blue-500 dark:border-blue-500">
            {{ step_two_svg | safe }}
          </div>
            <div class="absolute top-0 -ml-10 text-center mt-16 w-32 text-xs font-medium uppercase dark:text-white text-gray-600">Step 2</div>
          </div>
          <div class="flex-auto border-t-2 border-blue-500 dark:border-blue-500"></div>
          <div class="flex items-center text-gray-500 relative">
            <div class="rounded-full transition duration-500 ease-in-out h-12 w-12 py-3 border-2 border-blue-500 dark:border-blue-500">
            {{ step_three_svg | safe }}
          </div>
            <div class="absolute top-0 -ml-10 text-center mt-16 w-32 text-xs font-medium uppercase dark:text-white text-gray-600">Confirm</div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="py-3">
    <div class="container px-4 mx-auto">
      <div class="p-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
        <div class="flex flex-wrap items-center justify-between -mx-4 pb-6 border-gray-400 border-opacity-20"> </div>
        <form method="post" autocomplete="off" id='form'>
          <div class="py-3 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Select Network</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="relative">
                    {{ input_down_arrow_offer_svg | safe }}
                    <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                    {{ select_network_svg | safe }}
                  </div>
                  <select class="cursor-not-allowed disabled-select pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-" name="addr_to" disabled>
                      <option{% if data.addr_to=="-1" %} selected{% endif %} value="-1">Public Network</option>
                        {% for a in addrs_to %}
                        <option{% if data.addr_to==a[0] %} selected{% endif %} value="{{ a[0] }}">{{ a[0] }} {{ a[1] }}</option>
                        {% endfor %}
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Select Address</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="relative">
                    {{ input_down_arrow_offer_svg | safe }}
                    <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                    {{ select_address_svg | safe }}
                  </div>
                  <select class="cursor-not-allowed disabled-select hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="addr_from" disabled>
                    {% for a in addrs %}
                    <option{% if data.addr_from==a[0] %} selected{% endif %} value="{{ a[0] }}">{{ a[0] }} {{ a[1] }}</option>
                    {% endfor %}
                    <option{% if data.addr_from=="-1" %} selected{% endif %} value="-1">New Address</option>
                  </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Swap Type</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="relative">
                    {{ input_down_arrow_offer_svg | safe }}
                    <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                    {{ select_swap_type_svg | safe }}
                  </div>
                      <select class="cursor-not-allowed disabled-select pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="swap_type" id="swap_type" disabled>
                        {% for a in swap_types %}
                        <option{% if data.swap_type==a[0] %} selected{% endif %} value="{{ a[0] }}">{{ a[1] }}</option>
                        {% endfor %}
                      </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">You Send</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="flex flex-wrap -m-3">
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Coin You Send:</p>
                      <div class="custom-select">
                        <div class="relative">
                          {{ input_down_arrow_offer_svg | safe }}
                          <select class="select cursor-not-allowed disabled-select hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-5 focus:ring-0" id="coin_from" name="coin_from" disabled>
                            <option value="-1">Select coin you send</option>
                            {% for c in coins_from %}
                            <option{% if data.coin_from==c[0] %} selected{% endif %} value="{{ c[0] }}" data-image="/static/images/coins/{{ c[1]|replace(" ", "-") }}-20.png">{{ c[1] }}
                            </option>
                            {% endfor %}
                          </select>
                          <div class="select-dropdown">
                            <img class="select-icon" src="" alt=""> <img class="select-image" src="" alt=""> </div>
                        </div>
                      </div>
                    </div>
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Amount You Send</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none"> </div> <input class="cursor-not-allowed disabled-input hover:border-blue-500 pr-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-5 focus:ring-0" type="text" id="amt_from" name="amt_from" value="{{ data.amt_from }}" readonly>
                      </div>
                    </div>
                    {% if data.swap_style == 'xmr' %}
                    {% if data.coin_from | int in (6, 9) %} {# Not XMR or WOW #}
                    {% else %}
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Fee From Confirm Target</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ select_target_svg | safe }}
                      </div>
                      <input class="cursor-not-allowed disabled-input pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" type="number" name="fee_from_conf" min="1" max="32" value="{{ data.fee_from_conf }}" readonly>
                      </div>
                    </div>
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Fee Rate From</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ select_rate_svg | safe }}
                      </div> <input class="cursor-not-allowed disabled-input pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="fee_rate_from" value="{{ data.from_fee_override }}" readonly>
                      </div> <span class="text-sm mt-2 block dark:text-white"> <b>Fee Rate Source:</b> {{ data.from_fee_src }} </span>
                    </div>
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Fee From Increase By</p>
                      <div class="relative"> <svg class="absolute right-4 top-1/2 transform -translate-y-1/2" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M11.3333 6.1133C11.2084 5.98913 11.0395 5.91943 10.8633 5.91943C10.6872 5.91943 10.5182 5.98913 10.3933 6.1133L8.00001 8.47329L5.64001 6.1133C5.5151 5.98913 5.34613 5.91943 5.17001 5.91943C4.99388 5.91943 4.82491 5.98913 4.70001 6.1133C4.63752 6.17527 4.58792 6.249 4.55408 6.33024C4.52023 6.41148 4.50281 6.49862 4.50281 6.58663C4.50281 6.67464 4.52023 6.76177 4.55408 6.84301C4.58792 6.92425 4.63752 6.99799 4.70001 7.05996L7.52667 9.88663C7.58865 9.94911 7.66238 9.99871 7.74362 10.0326C7.82486 10.0664 7.912 10.0838 8.00001 10.0838C8.08801 10.0838 8.17515 10.0664 8.25639 10.0326C8.33763 9.99871 8.41136 9.94911 8.47334 9.88663L11.3333 7.05996C11.3958 6.99799 11.4454 6.92425 11.4793 6.84301C11.5131 6.76177 11.5305 6.67464 11.5305 6.58663C11.5305 6.49862 11.5131 6.41148 11.4793 6.33024C11.4454 6.249 11.3958 6.17527 11.3333 6.1133Z" fill="#8896AB"></path>
                        </svg>
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ select_rate_svg | safe }}
                      </div>
                      <select class="cursor-not-allowed disabled-select pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="fee_from_extra" disabled>
                          <option value="0">None</option>
                          <option value="10" {% if data.fee_from_extra==10 %} selected{% endif %}>10%</option>
                          <option value="50" {% if data.fee_from_extra==50 %} selected{% endif %}>50%</option>
                          <option value="100" {% if data.fee_from_extra==100 %} selected{% endif %}>100%</option>
                        </select>
                      </div> <span class="text-sm mt-2 block dark:text-white"> <b>Lock Tx Spend Fee:</b> {{ data.amt_from_lock_spend_tx_fee }} {{ data.tla_from }} </span>
                    </div>
                    {% endif %}
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">You Get</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="flex flex-wrap -m-3">
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Coin You Get</p>
                      <div class="custom-select">
                        <div class="relative">
                          {{ input_down_arrow_offer_svg | safe }}
                          <select class="cursor-not-allowed disabled-select select hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-5 focus:ring-0" id="coin_to" name="coin_to" disabled>
                            <option value="-1"></option>
                            {% for c in coins %}
                            <option{% if data.coin_to==c[0] %} selected{% endif %} value="{{ c[0] }}" data-image="/static/images/coins/{{ c[1]|replace(" ", "-") }}-20.png">{{ c[1] }}</option>
                            {% endfor %}
                          </select>
                          <div class="select-dropdown"> <img class="select-icon" src="" alt="">
                            <img class="select-image" src="" alt="">
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Amount You Get</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none"> </div> <input class="cursor-not-allowed disabled-input hover:border-blue-500 pr-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-5 focus:ring-0" type="text" id="amt_to" name="amt_to" value="{{ data.amt_to }}" readonly>
                      </div>
                    </div>
                    {% if data.swap_style == 'xmr' %}
                    {% if data.coin_to | int in (6, 9) %} {# Not XMR or WOW #}
                    {% else %}
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Fee To Confirm Target</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ select_target_svg | safe }}
                      </div>
                      <input class="cursor-not-allowed disabled-input pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" type="number" name="fee_to_conf" min="1" max="32" value="{{ data.fee_to_conf }}" readonly>
                      </div>
                    </div>
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Fee Rate To</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ select_rate_svg | safe }}
                      </div>
                      <input class="cursor-not-allowed disabled-input pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="fee_rate_to" value="{{ data.to_fee_override }}" readonly>
                      </div> <span class="text-sm mt-2 block dark:text-white"> <b>Fee Rate Source:</b> {{ data.to_fee_src }} </span>
                    </div>
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Fee To Increase By</p>
                      <div class="relative">
                        {{ input_down_arrow_offer_svg | safe }}
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ select_rate_svg | safe }}
                      </div>
                    <select class="cursor-not-allowed disabled-select pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="fee_to_extra" disabled>
                          <option value="0">None</option>
                          <option value="10" {% if data.fee_to_extra==10 %} selected{% endif %}>10%</option>
                          <option value="50" {% if data.fee_to_extra==50 %} selected{% endif %}>50%</option>
                          <option value="100" {% if data.fee_to_extra==100 %} selected{% endif %}>100%</option>
                      </select>
                      </div>
                    </div>
                    {% endif %}
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Bid Amount</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="flex flex-wrap -m-3">
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Minimum Purchase</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ select_bid_amount_svg | safe }}
                      </div>
                      <input class="cursor-not-allowed disabled-input pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" type="text" id="amt_bid_min" name="amt_bid_min" value="{{ data.amt_bid_min }}" title="Bids with an amount below the minimum bid value will be discarded" readonly>
                      </div>
                    </div>
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Rate</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ select_rate_svg | safe }}
                      </div>
                      <input class="cursor-not-allowed disabled-input pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" type="text" id="rate" name="rate" value="{{ data.rate }}" readonly>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Time</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="flex flex-wrap -m-3">
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Offer valid (hrs)</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ input_time_svg | safe }}
                      </div>
                      <input class="cursor-not-allowed disabled-input pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" type="number" name="validhrs" min="1" max="48" value="{{ data.validhrs }}" readonly>
                      </div>
                    </div>
                    {% if data.debug_ui == true %}
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Contract Locked (Mins)</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ input_time_svg | safe }}
                      </div>
                      <input class="cursor-not-allowed disabled-input pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" type="number" name="lockmins" min="10" max="5000" value="{{ data.lockmins }}" readonly>
                      </div>
                      {% if data.swap_style != 'xmr' %}
                      <div class="text-sm text-gray-500 mt-1.5 dark:text-white">(Participate txn will be locked for half the time.)</div>
                      {% endif %}
                    </div>
                    {% else %}
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Contract locked (Hours)</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ input_time_svg | safe }}
                      </div>
                      <input class="cursor-not-allowed disabled-input pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" type="number" name="lockhrs" min="1" max="96" value="{{ data.lockhrs }}" readonly>
                      </div>
                      {% if data.swap_style != 'xmr' %}
                      <div class="text-sm text-gray-500 mt-1.5 dark:text-white">(Participate txn will be locked for half the time.)</div>
                      {% endif %}
                    </div>
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Strategy</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="flex flex-wrap -m-3">
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Auto Accept Strategy</p>
                      <div class="relative">
                        {{ input_down_arrow_offer_svg | safe }}
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ select_auto_strategy_svg | safe }}
                      </div>
                      <select class="cursor-not-allowed disabled-select pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="automation_strat_id" disabled>
                          <option value="-1" {% if data.automation_strat_id==-1 %} selected{% endif %}>None</option>
                          {% for a in automation_strategies %}
                          <option value="{{ a[0] }}" {% if data.automation_strat_id==a[0] %} selected{% endif %}>{{ a[1] }}</option>
                          {% endfor %}
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Options</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="flex form-check form-check-inline">
                    <div class="flex items-center h-5"> <input class="cursor-not-allowed hover:border-blue-500 w-5 h-5 form-check-input text-blue-600 bg-gray-50 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-1 dark:bg-gray-500 dark:border-gray-400" type="checkbox" id="amt_var" name="amt_var" value="av" {% if data.amt_var==true %} checked="checked" {% endif %} disabled> </div>
                    <div class="ml-2 text-sm">
                      <label class="form-check-label text-sm font-medium text-gray-800 dark:text-white" for="inlineCheckbox2" style="opacity: 0.40;">Amount Variable</label>
                      <p id="helper-checkbox-text" class="text-xs font-normal text-gray-500 dark:text-gray-300" style="opacity: 0.40;">Allow bids with a different amount to the offer.</p>
                    </div>
                  </div>
                  <div class="flex mt-2 form-check form-check-inline">
                    <div class="flex items-center h-5"> <input class="cursor-not-allowed hover:border-blue-500 w-5 h-5 form-check-input text-blue-600 bg-gray-50 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-1 dark:bg-gray-500 dark:border-gray-400" type="checkbox" id="rate_var" name="rate_var" value="rv" {% if data.rate_var==true %} checked="checked" {% endif %} disabled> </div>
                    <div class="ml-2 text-sm">
                      <label class="form-check-label text-sm font-medium text-gray-800 dark:text-white" for="inlineCheckbox3" style="opacity: 0.40;">Rate Variable</label>
                      <p id="helper-checkbox-text" class="text-xs font-normal text-gray-500 dark:text-gray-300" style="opacity: 0.40;">Allow bids with a different rate to the offer.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
      </div>
    </div>
  </section>
  <section>
    <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
      <div class="pb-6 border-coolGray-100">
        <div class="flex flex-wrap items-center justify-between -m-2">
          <div class="w-full pt-2">
            <div class="container mt-5 mx-auto">
              <div class="pt-6 pb-6 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
                <div class="px-6">
                  <div class="flex flex-wrap justify-end">
                    <div class="w-full md:w-auto p-1.5">
                      <button name="step2" type="submit" value="Back" class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-coolGray-500 hover:text-coolGray-600 border border-coolGray-200 hover:border-coolGray-300 bg-white rounded-md focus:ring-0 focus:outline-none dark:text-white dark:hover:text-white dark:bg-gray-600 dark:hover:bg-gray-700 dark:border-gray-600 dark:hover:border-gray-600">
                      <span>Back</span>
                    </button>
                  </div>
                    <div class="w-full md:w-auto p-1.5">
                      <button name="submit_offer" value="Continue" type="submit" class="flex flex-wrap justify-center w-full px-4 py-2.5 bg-blue-500 hover:bg-green-600 hover:border-green-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
                        <span>Confirm</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <input type="hidden" name="formid" value="{{ form_id }}">
  <input type="hidden" name="addr_to" value="{{ data.addr_to }}">
  <input type="hidden" name="addr_from" value="{{ data.addr_from }}">
  <input type="hidden" name="swap_type" value="{{ data.swap_type }}">
  <input type="hidden" name="coin_from" value="{{ data.coin_from }}">
  <input type="hidden" name="fee_from_extra" value="{{ data.fee_from_extra }}">
  <input type="hidden" name="coin_to" value="{{ data.coin_to }}">
  <input type="hidden" name="fee_to_extra" value="{{ data.fee_to_extra }}">
  {% if data.automation_strat_id != -1 %}
  <input type="hidden" name="automation_strat_id" value="{{ data.automation_strat_id }}">
  {% endif %}
  {% if data.amt_var==true %}
  <input type="hidden" name="amt_var" value="av">
  {% endif %}
  {% if data.rate_var==true %}
  <input type="hidden" name="rate_var" value="rv">
{% endif %}
</form>
  <script src="static/js/new_offer.js"></script>
</div>
</div>
</div>
</div>
{% include 'footer.html' %}
</body>
</html>
