{% from 'style.html' import circular_info_messages_svg, green_cross_close_svg, red_cross_close_svg, circular_error_messages_svg %}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  {% if refresh %}
  <meta http-equiv="refresh" content="{{ refresh }}">
  {% endif %}
  <title>(BSX) BasicSwap - v{{ version }}</title>
  <link rel="icon" sizes="32x32" type="image/png" href="/static/images/favicon/favicon-32.png">
  <!-- CSS Stylesheets -->
  <link type="text/css" media="all" href="/static/css/libs/flowbite.min.css" rel="stylesheet">
  <link type="text/css" media="all" href="/static/css/libs/tailwind.min.css" rel="stylesheet">
  <!-- Custom styles -->
  <link type="text/css" media="all" href="/static/css/style.css" rel="stylesheet">
  <script>
    function getAPIKeys() {
      return {
        cryptoCompare: "{{ chart_api_key|safe }}",
        coinGecko: "{{ coingecko_api_key|safe }}"
      };
    }

    (function() {
      Object.defineProperty(window, 'ws_port', {
        value: "{{ ws_port|safe }}",
        writable: false,
        configurable: false,
        enumerable: true
      });
      window.getWebSocketConfig = window.getWebSocketConfig || function() {
        return {
          port: window.ws_port || '11700',
          fallbackPort: '11700'
        };
      };
    })();

    (function() {
      const isDarkMode = localStorage.getItem('color-theme') === 'dark' ||
        (!localStorage.getItem('color-theme') && window.matchMedia('(prefers-color-scheme: dark)').matches);

      if (!localStorage.getItem('color-theme')) {
        localStorage.setItem('color-theme', 'dark');
      }
      document.documentElement.classList.toggle('dark', isDarkMode);
    })();
  </script>
  <!-- Third-party Libraries -->
  <script src="/static/js/libs/chart.js"></script>
  <script src="/static/js/libs/chartjs-adapter-date-fns.bundle.min.js"></script>
  <script src="/static/js/libs/popper.js"></script>
  <script src="/static/js/libs/tippy.js"></script>
  <!-- UI Components -->
  <script src="/static/js/ui/tabs.js"></script>
  <script src="/static/js/ui/dropdown.js"></script>
  <!-- Core functionality -->
  <script src="/static/js/modules/coin-manager.js"></script>
  <script src="/static/js/modules/config-manager.js"></script>
  <script src="/static/js/modules/cache-manager.js"></script>
  <script src="/static/js/modules/cleanup-manager.js"></script>
  <script src="/static/js/modules/websocket-manager.js"></script>
  <script src="/static/js/modules/network-manager.js"></script>
  <script src="/static/js/modules/api-manager.js"></script>
  <script src="/static/js/modules/price-manager.js"></script>
  <script src="/static/js/modules/tooltips-manager.js"></script>
  <script src="/static/js/modules/notification-manager.js"></script>
  <script src="/static/js/modules/identity-manager.js"></script>
  <script src="/static/js/modules/summary-manager.js"></script>
  {% if current_page == 'wallets' or current_page == 'wallet' %}
  <script src="/static/js/modules/wallet-manager.js"></script>
  {% endif %}
  <!-- Memory management -->
  <script src="/static/js/modules/memory-manager.js"></script>
  <!-- Main application script -->
  <script src="/static/js/global.js"></script>
</head>
    <script>
    (function() {
      const isDarkMode = localStorage.getItem('color-theme') === 'dark' ||
        (!localStorage.getItem('color-theme') && window.matchMedia('(prefers-color-scheme: dark)').matches);

      if (!localStorage.getItem('color-theme')) {
        localStorage.setItem('color-theme', 'dark');
      }
      document.documentElement.classList.toggle('dark', isDarkMode);
    })();
    </script>

    <link rel=icon sizes="32x32" type="image/png" href="/static/images/favicon/favicon-32.png">
    <title>(BSX) BasicSwap - v{{ version }}</title>
  </head>
  <body class="dark:bg-gray-700">
    <section class="py-24 md:py-32">
      <div class="container px-4 mx-auto">
        <div class="max-w-sm mx-auto">
          <div class="mb-3 text-center">
            <a class="inline-block mb-6" href="#">
              <img src="/static/images/logos/basicswap-logo.svg" class="h-20 imageshow dark-image">
              <img src="/static/images/logos/basicswap-logo-dark.svg" class="h-20 imageshow light-image">
            </a>
            <p class="text-lg text-coolGray-500 font-medium mb-6 dark:text-white">Unlock your wallets</p>
            {% for m in messages %}
            <section class="py-4" id="messages_{{ m[0] }}" role="alert">
              <div class="container px-4 mx-auto">
                <div class="p-6 text-green-800 rounded-lg bg-green-50 border border-green-500 dark:bg-gray-500 dark:text-green-400 rounded-md">
                  <div class="flex flex-wrap justify-between items-center -m-2">
                    <div class="flex-1 p-2">
                      <div class="flex flex-wrap -m-1">
                        <div class="w-auto p-1"> {{ circular_info_messages_svg | safe }} </div>
                        <ul class="ml-4 mt-1">
                          <li class="font-semibold text-sm text-green-500 error_msg text-left"><span class="bold">ALERT:</span></li>
                          <li class="font-medium text-sm text-green-500 infomsg">This will unlock the system for all users!</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
            {% endfor %}
            {% for m in err_messages %}
            <section class="py-4" id="err_messages_{{ m[0] }}" role="alert">
              <div class="container px-4 mx-auto">
                <div class="p-6 text-green-800 rounded-lg bg-red-50 border border-red-400 dark:bg-gray-500 dark:text-red-400 rounded-md">
                  <div class="flex flex-wrap justify-between items-center -m-2">
                    <div class="flex-1 p-2">
                      <div class="flex flex-wrap -m-1">
                        <div class="w-auto p-1"> {{ circular_error_messages_svg | safe }} </div>
                        <ul class="ml-4 mt-1">
                          <li class="font-semibold text-sm text-red-500 error_msg text-left"><span class="bold">ERROR:</span></li>
                          <li class="font-medium text-sm text-red-500 error_msg">{{ m[1] }}</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
            {% endfor %}
          </div>
          <form method="post" autocomplete="off">
            <div class="mb-4">
              <label class="block mb-2 text-coolGray-800 font-medium dark:text-white" for="">Your Password</label>
              <div class="relative w-full">
                <div class="absolute inset-y-0 right-0 flex items-center px-2">
                  <input class="hidden js-password-toggle" id="toggle" type="checkbox" />
                  <label class="px-2 py-1 text-sm text-gray-600 font-mono cursor-pointer js-password-label" for="toggle">
                    <svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 24 24">
                      <g fill="#8896ab">
                        <path d="M23.444,10.239C21.905,8.062,17.708,3,12,3S2.1,8.062.555,10.24a3.058,3.058,0,0,0,0,3.52h0C2.1,15.938,6.292,21,12,21s9.905-5.062,11.445-7.24A3.058,3.058,0,0,0,23.444,10.239ZM12,17a5,5,0,1,1,5-5A5,5,0,0,1,12,17Z" fill="#8896ab"></path>
                      </g>
                    </svg>
                  </label>
                </div>
                <input class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-50 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0 js-password" name="password" id="password" type="password" autocomplete="off" />
              </div>
            </div>
            <button type="submit" name="unlock" value="Unlock" class="appearance-none focus:outline-none inline-block py-3 px-7 mb-6 w-full text-base text-blue-50 font-medium text-center leading-6 bg-blue-500 hover:bg-blue-600 focus:ring-0 rounded-md shadow-sm">Unlock</button>
            <p class="text-center">
              <span class="text-xs font-medium dark:text-white">Need help?</span>
              <a class="inline-block text-xs font-medium text-blue-500 hover:text-blue-600 hover:underline" href="https://academy.particl.io/en/latest/faq/get_support.html" target="_blank">Help / Tutorials</a>
            </p>
            <p class="text-center">
              <span class="text-xs font-medium text-coolGray-500 dark:text-gray-500">{{ title }}</span>
            </p>
            <input type="hidden" name="formid" value="{{ form_id }}">
          </form>
        </div>
      </div>
    </section>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
      const passwordToggle = document.querySelector('.js-password-toggle');
      if (passwordToggle) {
        passwordToggle.addEventListener('change', function() {
          const password = document.querySelector('.js-password');
          const passwordLabel = document.querySelector('.js-password-label');
          if (password && passwordLabel) {
            if (password.type === 'password') {
              password.type = 'text';
              passwordLabel.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 24 24"><g fill="#8896ab"><path d="M23.444,10.239a22.936,22.936,0,0,0-2.492-2.948l-4.021,4.021A5.026,5.026,0,0,1,17,12a5,5,0,0,1-5,5,5.026,5.026,0,0,1-.688-.069L8.055,20.188A10.286,10.286,0,0,0,12,21c5.708,0,9.905-5.062,11.445-7.24A3.058,3.058,0,0,0,23.444,10.239Z" fill="#8896ab"></path><path d="M12,3C6.292,3,2.1,8.062.555,10.24a3.058,3.058,0,0,0,0,3.52h0a21.272,21.272,0,0,0,4.784,4.9l3.124-3.124a5,5,0,0,1,7.071-7.072L8.464,15.536l10.2-10.2A11.484,11.484,0,0,0,12,3Z" fill="#8896ab"></path><path data-color="color-2" d="M1,24a1,1,0,0,1-.707-1.707l22-22a1,1,0,0,1,1.414,1.414l-22,22A1,1,0,0,1,1,24Z"></path></g></svg>';
            } else {
              password.type = 'password';
              passwordLabel.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 24 24"><g fill="#8896ab" ><path d="M23.444,10.239C21.905,8.062,17.708,3,12,3S2.1,8.062.555,10.24a3.058,3.058,0,0,0,0,3.52h0C2.1,15.938,6.292,21,12,21s9.905-5.062,11.445-7.24A3.058,3.058,0,0,0,23.444,10.239ZM12,17a5,5,0,1,1,5-5A5,5,0,0,1,12,17Z" fill="#8896ab"></path></g></svg>';
            }
            password.focus();
          }
        });
      }

      function toggleImages() {
        const html = document.querySelector('html');
        const darkImages = document.querySelectorAll('.dark-image');
        const lightImages = document.querySelectorAll('.light-image');

        if (html && html.classList.contains('dark')) {
          toggleImageDisplay(darkImages, 'block');
          toggleImageDisplay(lightImages, 'none');
        } else {
          toggleImageDisplay(darkImages, 'none');
          toggleImageDisplay(lightImages, 'block');
        }
      }

      function toggleImageDisplay(images, display) {
        images.forEach(img => {
          img.style.display = display;
        });
      }

      toggleImages();
    });
    </script>
  </body>
</html>
