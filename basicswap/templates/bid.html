{% include 'header.html' %}
{% from 'style.html' import breadcrumb_line_svg, circular_arrows_svg, input_arrow_down_svg, small_arrow_white_right_svg %}

<div class="container mx-auto">
 <section class="p-5 mt-5">
  <div class="flex flex-wrap items-center -m-2">
   <div class="w-full md:w-1/2 p-2">
    <ul class="flex flex-wrap items-center gap-x-3 mb-2">
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/">
       <p>Home</p>
      </a>
     </li>
      <li> {{ breadcrumb_line_svg | safe }} </li>
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="#">Bids</a>
     </li>
      <li> {{ breadcrumb_line_svg | safe }} </li>
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="{{ bid_id }}">BID ID: {{ bid_id }}</a>
     </li>
    </ul>
   </div>
  </div>
 </section>
 <section class="py-3">
  <div class="container px-4 mx-auto">
   <div class="relative py-11 px-16 bg-coolGray-900 dark:bg-blue-500 rounded-md overflow-hidden">
    <img class="absolute z-10 left-4 top-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute z-10 right-4 bottom-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute h-64 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 object-cover" src="/static/images/elements/wave.svg" alt="">
    <div class="relative z-20 flex flex-wrap items-center -m-3">
     <div class="w-full md:w-1/2 p-3">
      <h2 class="mb-6 text-4xl font-bold text-white tracking-tighter">Bid {% if debug_mode == true %} (Debug: bid template) {% endif %}</h2>
      <p class="font-normal text-coolGray-200 dark:text-white"><span class="bold">BID ID:</span> {{ bid_id }}</p>
     </div>
     <div class="w-full md:w-1/2 p-3 p-6 container flex flex-wrap items-center justify-end items-center mx-auto">
      {% if refresh %} 
      <a id="refresh" href="/bid/{{ bid_id }}"  class="rounded-full flex flex-wrap justify-center px-5 py-3 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border dark:bg-gray-500 dark:hover:bg-gray-700 border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
        {{ circular_arrows_svg | safe }}
       <span>Refresh {{ refresh }} seconds</span>
      </a>
      {% else %}
      <a id="refresh" href="/bid/{{ bid_id }}"  class="rounded-full flex flex-wrap justify-center px-5 py-3 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border dark:bg-gray-500 dark:hover:bg-gray-700 border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
        {{ circular_arrows_svg | safe }}
       <span>Refresh</span>
      </a>
    {% endif %}
      </div>
    </div>
   </div>
  </div>
 </section>
 {% include 'inc_messages.html' %}
 <section>
  <div class="pl-6 pr-6 pt-0 pb-0 mt-5 h-full overflow-hidden">
   <div class="pb-6 border-coolGray-100">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="container mt-5 mx-auto">
       <div class="pt-6 pb-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
        <div class="px-6">
         <div class="w-full mt-6 pb-6 overflow-x-auto">
          <table class="w-full min-w-max text-sm">
           <thead class="uppercase">
            <tr class="text-left">
             <th class="p-0">
              <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
               <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Options</span>
              </div>
             </th>
             <th class="p-0">
              <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
               <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Details</span>
              </div>
             </th>
            </tr>
           </thead>
           {% if data.was_sent == 'True' %}
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Swap</td>
            <td class="py-3 px-6">
             <div class="content flex py-2">
              <span class="bold">{{ data.amt_to }} {{ data.ticker_to }}</span>
                {{ small_arrow_white_right_svg | safe }}
              <span class="text-xs bold">{{ data.amt_from }} {{ data.ticker_from }}</span>
             </div>
            </td>
           </tr>
           {% else %}
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Swap</td>
            <td class="py-3 px-6">
             <div class="content flex py-2">
              <span class="bold">{{ data.amt_from }} {{ data.ticker_from }}</span>
                {{ small_arrow_white_right_svg | safe }}
              <span class="bold">{{ data.amt_to }} {{ data.ticker_to }}</span>
             </div>
            </td>
           </tr>
           {% endif %}
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Bid Rate</td>
            <td class="py-3 px-6">{{ data.bid_rate }}</td>
           </tr>
           {% if data.was_sent == 'True' %}
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">You Send</td>
            <td class="py-3 px-6">
             <span class="inline-flex align-middle items-center justify-center w-9 h-10 bg-white-50 rounded">
              <img class="h-7" src="/static/images/coins/{{ data.coin_to }}.png" alt="{{ data.coin_to }}">
             </span>{{ data.coin_to }}
            </td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">You Get</td>
            <td class="py-3 px-6">
             <span class="inline-flex align-middle items-center justify-center w-9 h-10 bg-white-50 rounded">
              <img class="h-7" src="/static/images/coins/{{ data.coin_from }}.png" alt="{{ data.coin_from }}">
             </span>{{ data.coin_from }}
            </td>
           </tr> {% else %} <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">You Send</td>
            <td class="py-3 px-6">
             <span class="inline-flex align-middle items-center justify-center w-9 h-10 bg-white-50 rounded">
              <img class="h-7" src="/static/images/coins/{{ data.coin_from }}.png" alt="{{ data.coin_from }}">
             </span>{{ data.coin_from }}
            </td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">You Get</td>
            <td class="py-3 px-6">
             <span class="inline-flex align-middle items-center justify-center w-9 h-10 bg-white-50 rounded">
              <img class="h-7" src="/static/images/coins/{{ data.coin_to }}.png" alt="{{ data.coin_to }}">
             </span>{{ data.coin_to }}
            </td>
           </tr> {% endif %} <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Bid State</td>
            <td class="py-3 px-6">{{ data.bid_state }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">State Description </td>
            <td class="py-3 px-6">{{ data.state_description }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Offer</td>
            <td class="py-3 px-6">
             <a class="monospace bold select-all" href="/offer/{{ data.offer_id }}">{{ data.offer_id }}</a>
            </td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Address From</td>
            <td class="py-3 px-6">
             <a class="monospace bold select-all" href="/identity/{{ data.addr_from }}">{{ data.addr_from }}</a> {{ data.addr_from_label }}
            </td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="flex items-center px-46 whitespace-nowrap">
             <svg alt="" class="w-5 h-5 rounded-full ml-5" xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 24 24">
              <g stroke-linecap="round" stroke-width="2" fill="none" stroke="#3B82F6" stroke-linejoin="round">
               <circle cx="12" cy="12" r="11"></circle>
               <polyline points=" 12,6 12,12 18,12 " stroke="#3B82F6"></polyline>
              </g>
             </svg>
             <div class="py-3 pl-2 bold">
              <div>Created At</div>
             </div>
            </td>
            <td class="py-3 px-6">{{ data.created_at }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="flex items-center px-46 whitespace-nowrap">
             <svg alt="" class="w-5 h-5 rounded-full  ml-5" xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 24 24">
              <g stroke-linecap="round" stroke-width="2" fill="none" stroke="#6b7280" stroke-linejoin="round">
               <circle cx="12" cy="12" r="11"></circle>
               <polyline points=" 12,6 12,12 18,12 " stroke="#6b7280"></polyline>
              </g>
             </svg>
             <div class="py-3 pl-2 bold">
              <div>Expired At</div>
             </div>
            </td>
            <td class="py-3 px-6">{{ data.expired_at }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Sent</td>
            <td class="py-3 px-6">{{ data.was_sent }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Received</td>
            <td class="py-3 px-6">{{ data.was_received }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Initiate Tx</td>
            <td class="py-3 px-6 monospace">{{ data.initiate_tx }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Initiate Conf</td>
            <td class="py-3 px-6">{{ data.initiate_conf }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Participate Tx</td>
            <td class="py-3 px-6 monospace">{{ data.participate_tx }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Participate Conf</td>
            <td class="py-3 px-6">{{ data.participate_conf }}</td>
           </tr>
           {% if data.show_txns %}
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Initiate Tx Refund</td>
            <td class="py-3 px-6 monospace">{{ data.initiate_tx_refund }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Participate Tx Refund</td>
            <td class="py-3 px-6 monospace">{{ data.participate_tx_refund }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Initiate Tx Spend Tx</td>
            <td class="py-3 px-6 monospace">{{ data.initiate_tx_spend }}</td>
           </tr>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold">Participate Tx Spend Tx</td>
            <td class="py-3 px-6 monospace">{{ data.participate_tx_spend }}</td>
           </tr>
           {% endif %}
          </table>
         </div>
        </div>
        </table>
       </div>
      </div>
     </div>
 </section>
 <section class="p-6">
  <div class="flex flex-wrap items-center">
   <div class="w-full">
    <h4 class="font-semibold text-black dark:text-white text-2xl">Old states</h4>
   </div>
  </div>
 </section>
 <section>
  <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
   <div class="pb-6 border-coolGray-100">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="container mt-5 mx-auto">
       <div class="pt-6 pb-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
        <div class="px-6">
         <div class="w-full mt-6 pb-6 overflow-x-auto">
          <table class="w-full min-w-max text-sm">
           <thead class="uppercase">
            <tr class="text-left">
             <th class="p-0">
              <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
               <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Set at time</span>
              </div>
             </th>
             <th class="p-0">
              <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
               <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Old states</span>
              </div>
             </th>
            </tr>
           </thead>
           {% for s in old_states %}
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="flex items-center whitespace-nowrap">
             <svg alt="" class="w-5 h-5 rounded-full  ml-5" xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 24 24">
              <g stroke-linecap="round" stroke-width="2" fill="none" stroke="#6b7280" stroke-linejoin="round">
               <circle cx="12" cy="12" r="11"></circle>
               <polyline points=" 12,6 12,12 18,12 " stroke="#6b7280"></polyline>
              </g>
             </svg>
             <div class="py-3 pl-2 bold">
              <div>{{ s[0] | formatts }}</div>
             </div>
            </td>
            <td class="py-3 px-6">{{ s[1] }}</td>
           </tr>
           {% endfor %}
          </table>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
 </section>
 {% if data.events %}
 <section class="p-6">
  <div class="flex flex-wrap items-center">
   <div class="w-full">
    <h4 class="font-semibold text-black dark:text-white text-2xl">Events</h4>
   </div>
  </div>
 </section>
 <section>
  <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
   <div class="pb-6 border-coolGray-100">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="container mt-5 mx-auto">
       <div class="pt-6 pb-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
        <div class="px-6">
         <div class="w-full mt-6 pb-6 overflow-x-auto">
          <table class="w-full min-w-max text-sm">
           <thead class="uppercase">
            <tr class="text-left">
             <th class="p-0">
              <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
               <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Time</span>
              </div>
             </th>
             <th class="p-0">
              <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
               <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Events</span>
              </div>
             </th>
            </tr>
           </thead>
           {% for e in data.events %}
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="flex items-center px-46 whitespace-nowrap">
             <svg alt="" class="w-5 h-5 rounded-full  ml-5" xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 24 24">
              <g stroke-linecap="round" stroke-width="2" fill="none" stroke="#6b7280" stroke-linejoin="round">
               <circle cx="12" cy="12" r="11"></circle>
               <polyline points=" 12,6 12,12 18,12 " stroke="#6b7280"></polyline>
              </g>
             </svg>
             <div class="py-3 pl-2 bold">
              <div>{{ e.at | formatts }}</div>
             </div>
            </td>
            <td class="py-3 px-6">{{ e.desc }}</td>
           </tr>
           {% endfor %}
          </table>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
 </section>
 {% else %}
 {% endif %}
 <form method="post"> {% if data.show_bidder_seq_diagram %} <section class="p-6">
   <div class="flex flex-wrap items-center">
    <div class="w-full">
     <h4 class="font-semibold text-black dark:text-white text-2xl">Bidder Sequence Diagram</h4>
    </div>
   </div>
  </section>
  <section>
   <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
    <div class="pb-6 border-coolGray-100">
     <div class="flex flex-wrap items-center justify-between -m-2">
      <div class="w-full pt-2">
       <div class="container mt-5 mx-auto">
        <div class="pt-6 pb-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
         <div class="px-6">
          <div class="w-full mt-6 pb-6 overflow-x-auto">
           <div class="overflow-x-auto items-center justify-center relative">
            <div class="flex items-center justify-center min-h-screen">
             <div class="flex items-center justify-between text-white">
              <img class="h-full py-2 pr-4 ml-8" src="/static/sequence_diagrams/bidder.alt.xu.min.svg">
             </div>
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </section>
  {% endif %}
  {% if data.show_offerer_seq_diagram %}
  <section class="p-6">
   <div class="flex flex-wrap items-center">
    <div class="w-full">
     <h4 class="font-semibold text-black dark:text-white text-2xl">Offerer Sequence Diagram</h4>
    </div>
   </div>
  </section>
  <section>
   <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
    <div class="pb-6 border-coolGray-100">
     <div class="flex flex-wrap items-center justify-between -m-2">
      <div class="w-full pt-2">
       <div class="container mt-5 mx-auto">
        <div class="pt-6 pb-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
         <div class="px-6">
          <div class="w-full mt-6 pb-6 overflow-x-auto">
           <div class="overflow-x-auto items-center justify-center relative">
            <div class="flex items-center justify-center min-h-screen">
             <div class="flex items-center justify-between text-white">
              <img class="h-full py-2 pr-4 ml-8" src="/static/sequence_diagrams/offerer.alt.xu.min.svg">
             </div>
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </section>
  {% endif %}
  {% if edit_bid %}
  <section>
   <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
    <div class="pb-6 border-coolGray-100">
     <div class="flex flex-wrap items-center justify-between -m-2">
      <div class="w-full pt-2">
       <div class="container mt-5 mx-auto">
        <div class="pt-6 pb-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
         <div class="px-6">
          <div class="w-full mt-6 pb-6 overflow-x-auto">
           <table class="w-full min-w-max text-sm">
            <thead class="uppercase">
             <tr class="text-left">
              <th class="p-0">
               <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
                <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Option</span>
               </div>
              </th>
              <th class="p-0">
               <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
                <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Settings</span>
               </div>
              </th>
             </tr>
            </thead>
            <tr class="opacity-100 text-gray-500 dark:text-gray-100">
             <td class="py-3 px-6 bold">Change Bid State:</td>
             <td class="py-3 px-6">
              <div class="relative">
                {{ input_arrow_down_svg| safe }}
               <select class="bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-50 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-" name="new_state">
                {% for s in data.bid_states %}
                <option value="{{ s[0] }}" {% if data.bid_state_ind==s[0] %} selected{% endif %}>{{ s[1] }}</option>
                {% endfor %}
                </select>
              </div>
             </td>
            </tr>
            {% if data.debug_ui == true %}
            <tr class="opacity-100 text-gray-500 dark:text-gray-100">
             <td class="py-3 px-6 bold">Debug Option</td>
             <td class="py-3 px-6">
              <div class="relative">
                {{ input_arrow_down_svg| safe }}
               <select class="bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-50 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-" name="debugind">
                <option{% if data.debug_ind=="-1" %} selected{% endif %} value="-1">None</option>
                {% for a in data.debug_options %}
                <option{% if data.debug_ind==a[0] %} selected{% endif %} value="{{ a[0] }}">{{ a[1] }}</option>
                {% endfor %}
               </select>
              </div>
             </td>
            </tr>
              {% endif %}
           </table>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </section>
  <section>
   <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
    <div class="pb-6 border-coolGray-100">
     <div class="flex flex-wrap items-center justify-between -m-2">
      <div class="w-full pt-2">
       <div class="container mt-5 mx-auto">
        <div class="pt-6 pb-6 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
         <div class="px-6">
          <div class="flex flex-wrap justify-end">
           <div class="w-full md:w-auto p-1.5">
            <button name="edit_bid_cancel" value="Cancel" type="submit" class="lex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-coolGray-500 hover:text-coolGray-600 border border-coolGray-200 hover:border-coolGray-300 bg-white rounded-md shadow-button focus:ring-0 focus:outline-none dark:text-white dark:hover:text-white dark:bg-gray-600 dark:hover:bg-gray-700 dark:border-gray-600 dark:hover:border-gray-600">Cancel</button>
           </div>
           <div class="w-full md:w-auto p-1.5">
            <button name="edit_bid_submit" value="Submit" type="submit" class="flex flex-wrap justify-center w-full px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">Submit Edit</button>
           </div>
           {% else %}
           <section>
            <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
             <div class="pb-6 border-coolGray-100">
              <div class="flex flex-wrap items-center justify-between -m-2">
               <div class="w-full pt-2">
                <div class="container mt-5 mx-auto">
                 <div class="pt-6 pb-6 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
                  <div class="px-6">
                   <div class="flex flex-wrap justify-end">
                    {% if data.show_bidder_seq_diagram %}
                    <div class="w-full md:w-auto p-1.5">
                     <button name="hide_bidder_seq_diagram" type="submit" value="Hide Bidder Sequence Diagram" class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-coolGray-500 hover:text-coolGray-600 border border-coolGray-200 hover:border-coolGray-300 bg-white rounded-md focus:ring-0 focus:outline-none dark:text-white dark:hover:text-white dark:bg-gray-600 dark:hover:bg-gray-700 dark:border-gray-600 dark:hover:border-gray-600">Hide Bidder Sequence Diagram</button>
                    </div>
                    {% else %}
                    <div class="w-full md:w-auto p-1.5">
                     <button name="show_bidder_seq_diagram" type="submit" value="Show Bidder Sequence Diagram" class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-coolGray-500 hover:text-coolGray-600 border border-coolGray-200 hover:border-coolGray-300 bg-white rounded-md focus:ring-0 focus:outline-none dark:text-white dark:hover:text-white dark:bg-gray-600 dark:hover:bg-gray-700 dark:border-gray-600 dark:hover:border-gray-600">Show Bidder Sequence Diagram</button>
                    </div>
                    {% endif %}
                    {% if data.show_offerer_seq_diagram %}
                    <div class="w-full md:w-auto p-1.5">
                     <button name="hide_offerer_seq_diagram" type="submit" value="Hide Offerer Sequence Diagram" class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-coolGray-500 hover:text-coolGray-600 border border-coolGray-200 hover:border-coolGray-300 bg-white rounded-md focus:ring-0 focus:outline-none dark:text-white dark:hover:text-white dark:bg-gray-600 dark:hover:bg-gray-700 dark:border-gray-600 dark:hover:border-gray-600">Hide Offerer Sequence Diagram</button>
                    </div>
                    {% else %}
                    <div class="w-full md:w-auto p-1.5">
                     <button name="show_offerer_seq_diagram" type="submit" value="Show Offerer Sequence Diagram" class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-coolGray-500 hover:text-coolGray-600 border border-coolGray-200 hover:border-coolGray-300 bg-white rounded-md focus:ring-0 focus:outline-none dark:text-white dark:hover:text-white dark:bg-gray-600 dark:hover:bg-gray-700 dark:border-gray-600 dark:hover:border-gray-600">Show Offerer Sequence Diagram</button>
                    </div>
                    {% endif %}
                    {% if data.show_txns %}
                    <div class="w-full md:w-auto p-1.5">
                     <button name="hide_txns" type="submit" value="Hide Info" class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-coolGray-500 hover:text-coolGray-600 border border-coolGray-200 hover:border-coolGray-300 bg-white rounded-md shadow-button focus:ring-0 focus:outline-none dark:text-white dark:hover:text-white dark:bg-gray-600 dark:hover:bg-gray-700 dark:border-gray-600 dark:hover:border-gray-600">Hide More info</button>
                    </div>
                    {% else %}
                    <div class="w-full md:w-auto p-1.5">
                     <button name="show_txns" type="submit" value="Show More Info" class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-coolGray-500 hover:text-coolGray-600 border border-coolGray-200 hover:border-coolGray-300 bg-white rounded-md shadow-button focus:ring-0 focus:outline-none dark:text-white dark:hover:text-white dark:bg-gray-600 dark:hover:bg-gray-700 dark:border-gray-600 dark:hover:border-gray-600">Show More Info </button>
                    </div>
                    {% endif %}
                    {% if debug_ui_mode == true %}
                    <div class="w-full md:w-auto p-1.5">
                     <button name="edit_bid" type="submit" value="Edit Bid" class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-coolGray-500 hover:text-coolGray-600 border border-coolGray-200 hover:border-coolGray-300 bg-white rounded-md shadow-button focus:ring-0 focus:outline-none dark:text-white dark:hover:text-white dark:bg-gray-600 dark:hover:bg-gray-700 dark:border-gray-600 dark:hover:border-gray-600">Edit Bid</button>
                    </div>
                    {% endif %}
                    {% endif %}
                    {% if data.can_abandon == true and not edit_bid %}
                    <div class="w-full md:w-auto p-1.5">
                     <button name="abandon_bid" type="submit" value="Abandon Bid" onclick="return confirmPopup();" class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-white hover:text-red border border-red-500 hover:border-red-500 hover:bg-red-600 bg-red-500 rounded-md shadow-button focus:ring-0 focus:outline-none">Abandon Bid</button>
                    </div>
                    {% endif %}
                     {% if data.was_received == 'True' and not edit_bid and data.can_accept_bid %}
                    <div class="w-full md:w-auto p-1.5">
                     <button name="accept_bid" value="Accept Bid" type="submit" onclick='return confirmPopup("Accept");' class="flex flex-wrap justify-center w-full px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">Accept Bid</button>
                    </div>
                    {% endif %}
                    </div>
                  </div>
                 </div>
                </div>
               </div>
              </div>
             </div>
            </div>
           </section>
<div id="confirmModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
  <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity duration-300 ease-out"></div>
  <div class="relative z-50 min-h-screen px-4 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-500 rounded-lg max-w-md w-full p-6 shadow-lg transition-opacity duration-300 ease-out">
      <div class="text-center">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4" id="confirmTitle">Confirm Action</h2>
        <p class="text-gray-600 dark:text-gray-200 mb-6 whitespace-pre-line" id="confirmMessage">Are you sure?</p>
        <div class="flex justify-center gap-4">
          <button type="button" id="confirmYes" 
                  class="px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
            Confirm
          </button>
          <button type="button" id="confirmNo"
                  class="px-4 py-2.5 font-medium text-sm text-white hover:text-red border border-red-500 hover:border-red-500 hover:bg-red-600 bg-red-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
           <input type="hidden" name="formid" value="{{ form_id }}">
          </div>
         </div>
        </div>
       </div>
 </form>
 <script>
document.addEventListener('DOMContentLoaded', function() {
  let confirmCallback = null;
  let triggerElement = null;
  
  document.getElementById('confirmYes').addEventListener('click', function() {
    if (typeof confirmCallback === 'function') {
      confirmCallback();
    }
    hideConfirmDialog();
  });
  
  document.getElementById('confirmNo').addEventListener('click', hideConfirmDialog);
  
  function showConfirmDialog(title, message, callback) {
    confirmCallback = callback;
    document.getElementById('confirmTitle').textContent = title;
    document.getElementById('confirmMessage').textContent = message;
    const modal = document.getElementById('confirmModal');
    if (modal) {
      modal.classList.remove('hidden');
    }
    return false;
  }
  
  function hideConfirmDialog() {
    const modal = document.getElementById('confirmModal');
    if (modal) {
      modal.classList.add('hidden');
    }
    confirmCallback = null;
    return false;
  }
  
  window.confirmPopup = function(action = 'Abandon') {
    triggerElement = document.activeElement;
    const title = `Confirm ${action} Bid`;
    const message = `Are you sure you want to ${action.toLowerCase()} this bid?`;
    
    return showConfirmDialog(title, message, function() {
      if (triggerElement) {
        const form = triggerElement.form;
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = triggerElement.name;
        hiddenInput.value = triggerElement.value;
        form.appendChild(hiddenInput);
        form.submit();
      }
    });
  };
  
  const overrideButtonConfirm = function(button, action) {
    if (button) {
      button.removeAttribute('onclick');
      button.addEventListener('click', function(e) {
        e.preventDefault();
        triggerElement = this;
        return confirmPopup(action);
      });
    }
  };
  
  const abandonBidBtn = document.querySelector('button[name="abandon_bid"]');
  overrideButtonConfirm(abandonBidBtn, 'Abandon');
  
  const acceptBidBtn = document.querySelector('button[name="accept_bid"]');
  overrideButtonConfirm(acceptBidBtn, 'Accept');
});
 </script>
</div>
{% include 'footer.html' %}
</body>
</html>
