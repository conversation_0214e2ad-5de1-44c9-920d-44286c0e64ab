{% include 'header.html' %}
{% from 'style.html' import breadcrumb_line_svg, white_automation_svg, page_forwards_svg, page_back_svg, filter_apply_svg %}
<div class="container mx-auto">
 <section class="p-5 mt-5">
  <div class="flex flex-wrap items-center -m-2">
   <div class="w-full md:w-1/2 p-2">
    <ul class="flex flex-wrap items-center gap-x-3 mb-2">
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/">
       <p>Home</p>
      </a>
        <li>{{ breadcrumb_line_svg | safe }}</li>
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/automation">Automation Strategies</a>
     </li>
        <li>{{ breadcrumb_line_svg | safe }}</li>
    </ul>
    </ul>
   </div>
  </div>
 </section>
 <section class="py-4">
  <div class="container px-4 mx-auto">
   <div class="relative py-11 px-16 bg-coolGray-900 dark:bg-blue-500 rounded-md overflow-hidden">
    <img class="absolute z-10 left-4 top-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute z-10 right-4 bottom-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute h-64 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 object-cover" src="/static/images/elements/wave.svg" alt="">
    <div class="relative z-20 flex flex-wrap items-center -m-3">
     <div class="w-full md:w-1/2 p-3">
      <h2 class="text-4xl font-bold text-white tracking-tighter">Automation Strategies</h2>
     </div>
     <div class="w-full md:w-1/2 p-3 p-6 container flex flex-wrap items-center justify-end items-center mx-auto">
      <a href="/newautomationstrategy" class="rounded-full flex flex-wrap justify-center px-5 py-3 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border dark:bg-gray-500 dark:hover:bg-gray-700 border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
        {{ white_automation_svg | safe }}
       <span>Create New Strategy</span>
      </a>
     </div>
    </div>
   </div>
  </div>
 </section>
 {% include 'inc_messages.html' %}
 <section>
  <form method="post">
   <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
    <div class="border-coolGray-100">
     <div class="flex flex-wrap items-center justify-between -m-2">
      <div class="w-full pt-2">
       <div class="container mt-5 mx-auto">
        <div class="pt-6 pb-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
         <div class="px-6">
          <div class="w-full mt-6 pb-6 overflow-x-auto">
           <table class="w-full min-w-max text-sm">
            <thead class="uppercase">
             <tr class="text-left">
              <th class="p-0">
               <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
                <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Filters</span>
               </div>
              </th>
              <th class="p-0">
               <div class="py-3 px-6 bg-coolGray-200 dark:bg-gray-600">
                <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold py-3 px-6"></span>
               </div>
              </th>
              <th class="p-0">
               <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
                <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold py-3 px-6"></span>
               </div>
              </th>
             </tr>
            </thead>
            <tr class="opacity-100 text-gray-500 dark:text-gray-100">
             <td class="py-3 px-6 bold">Sort by:</td>
             <td class="py-3 px-6">
              <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-50 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="sort_by">
               <option value="created_at" {% if filters.sort_by=='created_at' %} selected{% endif %}>Created At</option>
              </select>
             </td>
             <td class="py-3 px-6">
              <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-50 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="sort_dir">
               <option value="asc" {% if filters.sort_dir=='asc' %} selected{% endif %}>Ascending</option>
               <option value="desc" {% if filters.sort_dir=='desc' %} selected{% endif %}>Descending</option>
              </select>
             </td>
            </tr>
           </table>
          </div>
         </div>
        </div>
       </div>
 </section>
 <section>
  <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden ">
   <div class="pb-6 ">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="container mx-auto">
       <div class="pt-6 pb-6 bg-coolGray-100 border-t border-gray-100 dark:border-gray-400 dark:bg-gray-500 rounded-bl-xl rounded-br-xl">
        <div class="px-6">
         <div class="flex flex-wrap justify-end">
          <div class="w-full md:w-auto p-1.5 ml-2">
           <button name='clearfilters' value="Clear Filters" class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm hover:text-white dark:text-white dark:bg-gray-500 bg-coolGray-200 hover:bg-green-600 hover:border-green-600 rounded-lg transition duration-200 border border-coolGray-200 dark:border-gray-400 rounded-md shadow-button focus:ring-0 focus:outline-none">Clear Filters</button>
          </div>
          <div class="w-full md:w-auto p-1.5 ml-2">
           <button name="" value="Submit" type="submit" class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-white bg-blue-600 hover:bg-green-600 hover:border-green-600 rounded-lg transition duration-200 border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
            {{ filter_apply_svg | safe }} Apply Filters</button>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
 </section>
 <section>
  <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
   <div class="border-coolGray-100">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="container mt-5 mx-auto">
       <div class="pt-6 pb-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
        <div class="px-6">
         <div class="w-full mt-6 pb-6 overflow-x-auto">
          <table class="w-full min-w-max text-sm">
           <thead class="uppercase">
            <tr class="text-left">
             <th class="p-0">
              <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
               <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Name</span>
              </div>
             </th>
             <th class="p-0">
              <div class="py-3 px-6 bg-coolGray-200 dark:bg-gray-600">
               <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Type</span>
              </div>
             </th>
            </tr>
           </thead>
           {% for s in strategies %}
           <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
            <td class="py-3 px-6 bold monospace">
             <a href=/automationstrategy/{{ s[0] }}>{{ s[1] }}</a>
            </td>
            <td class="py-3 px-6">{{ s[2] }}</td>
           </tr>
           {% endfor %}
          </table>
         </div>
        </div>
       </div>
      </div>
 </section>
 <section>
  <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden ">
   <div class="pb-6 ">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="container mx-auto">
       <div class="pt-6 pb-6 bg-coolGray-100 border-t border-gray-100 dark:border-gray-400 dark:bg-gray-500 rounded-bl-xl rounded-br-xl">
        <div class="px-6">
         <div class="flex flex-wrap justify-end">
          <div class="w-full md:w-auto p-1.5">
           <button type="submit" name='pageback' value="Page Back" class="inline-flex items-center h-9 py-1 px-4 text-xs text-blue-50 font-semibold bg-blue-500 hover:bg-blue-600 rounded-lg transition duration-200 focus:ring-0 focus:outline-none">
            {{ page_back_svg | safe }}
            <span>Previous</span>
           </button>
          </div>
          <div class="flex items-center">
           <div class="w-full md:w-auto p-1.5">
            <p class="text-sm font-heading dark:text-white">Page: {{ filters.page_no }}</p>
           </div>
          </div>
          <div class="w-full md:w-auto p-1.5">
           <button type="submit" name='pageforwards' value="Page Forwards" class="inline-flex items-center h-9 py-1 px-4 text-xs text-blue-50 font-semibold bg-blue-500 hover:bg-blue-600 rounded-lg transition duration-200 focus:ring-0 focus:outline-none">
            <span>Next</span>
            {{ page_forwards_svg | safe }}
           </button>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
 </section>
 <input type="hidden" name="formid" value="{{ form_id }}">
 <input type="hidden" name="pageno" value="{{ filters.page_no }}">
 </form>
</div>
{% include 'footer.html' %}
</div>
</body>
</html>