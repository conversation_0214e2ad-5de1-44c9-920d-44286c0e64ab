{% include 'header.html' %}
{% from 'style.html' import breadcrumb_line_svg, circular_arrows_svg %} 
<div class="container mx-auto">
 <section class="p-5 mt-5">
  <div class="flex flex-wrap items-center -m-2">
   <div class="w-full md:w-1/2 p-2">
    <ul class="flex flex-wrap items-center gap-x-3 mb-2">
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/">
       <p>Home</p>
      </a>
     </li>
    <li>{{ breadcrumb_line_svg | safe }}</li>
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/tor">Tor</a>
     </li>
    <li>{{ breadcrumb_line_svg | safe }}</li>
    </ul>
   </div>
  </div>
 </section>
 <section class="py-4">
  <div class="container px-4 mx-auto">
   <div class="relative py-11 px-16 bg-coolGray-900 dark:bg-blue-500 rounded-md overflow-hidden">
    <img class="absolute z-10 left-4 top-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute z-10 right-4 bottom-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute h-64 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 object-cover" src="/static/images/elements/wave.svg" alt="">
    <div class="relative z-20 flex flex-wrap items-center -m-3">
     <div class="w-full md:w-1/2 p-3">
      <h2 class="mb-6 text-4xl font-bold text-white tracking-tighter">Tor</h2>
      <p class="font-normal text-coolGray-200 dark:text-white">Tor connection information.</p>
     </div>
     <div class="w-full md:w-1/2 p-3 p-6 container flex flex-wrap items-center justify-end items-center mx-auto">
      {% if refresh %}
      <a id="refresh" href="/tor" class="rounded-full flex flex-wrap justify-center px-5 py-3 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border dark:bg-gray-500 dark:hover:bg-gray-700 border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
        {{ circular_arrows_svg | safe }}
       <span>Refresh {{ refresh }} seconds</span>
      </a>
      {% else %}
      <a id="refresh" href="/tor" class="rounded-full flex flex-wrap justify-center px-5 py-3 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border dark:bg-gray-500 dark:hover:bg-gray-700 border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
        {{ circular_arrows_svg | safe }}
       <span>Refresh</span>
      </a>
    {% endif %}
      </div>
    </div>
   </div>
  </div>
 </section>
 {% include 'inc_messages.html' %}
 <section>
 <div class="pl-6 pr-6 pt-0 pb-0 mt-5 h-full overflow-hidden">
  <div class="pb-6 border-coolGray-100">
   <div class="flex flex-wrap items-center justify-between -m-2">
    <div class="w-full pt-2">
     <div class="container mt-5 mx-auto">
      <div class="pt-6 pb-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
       <div class="px-6">
        <div class="w-full mt-6 pb-6 overflow-x-auto">
         <table class="w-full min-w-max text-sm">
          <thead class="uppercase">
           <tr class="text-left">
            <th class="p-0">
             <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
              <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Tor</span>
             </div>
            </th>
            <th class="p-0">
             <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
              <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Details</span>
             </div>
            </th>
           </tr>
          </thead>
          <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
           <td class="py-3 px-6 bold">Tor Mode</td>
           <td td class="py-3 px-6">{% if use_tor_proxy == true %} Active {% if tor_established == true %} & Connected{% endif %} {% endif %}</td>
          </tr>
          <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
           <td class="py-3 px-6 bold">Circuit Established</td>
           <td td class="py-3 px-6">{{ data.circuit_established }}</td>
          </tr>
          <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
           <td class="py-3 px-6 bold">Bytes Written</td>
           <td td class="py-3 px-6">{{ data.bytes_written }}</td>
          </tr>
          <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
           <td class="py-3 px-6 bold">Bytes Read</td>
           <td td class="py-3 px-6">{{ data.bytes_read }}</td>
          </tr>
         </table>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
 </div>
</div>
</section>
{% include 'footer.html' %}
</div>
</body>
</html>