{% include 'header.html' %}
{% from 'style.html' import breadcrumb_line_svg, input_arrow_down_svg %} 
<div class="container mx-auto">
 <section class="p-5 mt-5">
  <div class="flex flex-wrap items-center -m-2">
   <div class="w-full md:w-1/2 p-2">
    <ul class="flex flex-wrap items-center gap-x-3 mb-2">
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/">
       <p>Home</p>
      </a>
     </li>
    <li> {{ breadcrumb_line_svg | safe }} </li>
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/explores">Explorers</a>
     </li>
    <li> {{ breadcrumb_line_svg | safe }} </li>
    </ul>
   </div>
  </div>
 </section>
 <section class="py-4">
  <div class="container px-4 mx-auto">
   <div class="relative py-11 px-16 bg-coolGray-900 dark:bg-blue-500 rounded-md overflow-hidden">
    <img class="absolute z-10 left-4 top-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute z-10 right-4 bottom-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute h-64 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 object-cover" src="/static/images/elements/wave.svg" alt="">
    <div class="relative z-20 flex flex-wrap items-center -m-3">
     <div class="w-full md:w-1/2 p-3">
      <h2 class="mb-6 text-4xl font-bold text-white tracking-tighter">Explorers</h2>
      <p class="font-normal text-coolGray-200 dark:text-white">Query blockchain explorers with quick commands.</p>
     </div>
    </div>
   </div>
  </div>
 </section>
 {% include 'inc_messages.html' %}
 <section>
  <form method="post">
   <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden min-height-50">
    <div class="border-coolGray-100">
     <div class="flex flex-wrap items-center justify-between -m-2">
      <div class="w-full pt-2">
       <div class="container mt-5 mx-auto">
        <div class="pt-6 pb-6 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
         <div class="px-6">
          <div class="w-full mt-6 pb-6 overflow-x-auto">
           <table class="w-full min-w-max text-sm">
            <thead class="uppercase">
             <tr class="text-left">
              <th class="p-0">
               <div class="py-3 px-6 rounded-tl-xl  bg-coolGray-200 dark:bg-gray-600">
                <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Select Explorer</span>
               </div>
              </th>
              <th class="p-0">
               <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
                <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Action</span>
               </div>
              </th>
             </tr>
            </thead>
            <tr class="opacity-100 text-gray-500 dark:text-gray-100">
             <td class="py-3 px-6">
              <div class="relative">
                {{ input_arrow_down_svg| safe }} 
               <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-50 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="explorer">
                <!-- <option value="-1" {% if explorer==-1 %} selected{% endif %}>Select Explorer</option> -->
                {% for e in explorers %}
                <option value="{{ e[0] }}" {% if explorer==e[0] %} selected{% endif %}>{{ e[1] }}</option>
                {% endfor %}
               </select>
              </div>
             </td>
             <td class="py-3 px-6">
              <div class="relative">
                {{ input_arrow_down_svg| safe }}              
              <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-50 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="action">
               <option value="-1" {% if action==-1 %} selected{% endif %}>Select Action</option>
               {% for a in actions %} <option value="{{ a[0] }}" {% if action==a[0] %} selected{% endif %}>{{ a[1] }}</option>
               {% endfor %}
              </select>
            </div>
             </td>
            </tr>
            <tr class="opacity-100 text-gray-500 dark:text-gray-100">
             <td class="py-3 px-6 bold">Arguments:</td>
             <td class="py-3 px-6">
              <input class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-50 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" type="text" name="args" placeholder="Arguments">
             </td>
            </tr>
           </table>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
 </section>
 <section>
  <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden ">
   <div class="pb-6 ">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="container mx-auto">
       <div class="pt-6 pb-6 bg-coolGray-100 border-t border-gray-100 dark:border-gray-400 dark:bg-gray-500 rounded-bl-xl rounded-br-xl">
        <div class="px-6">
         <div class="flex flex-wrap justify-end">
          <div class="w-full md:w-auto p-1.5 ml-2">
           <button type="submit" class="flex flex-wrap justify-center w-full px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">Apply</button>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
 </section>
 <input type="hidden" name="formid" value="{{ form_id }}">
 {% if result %}
<section class="rounded-xl">
  <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
   <div class="border-coolGray-100">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="container mt-5 mx-auto">
       <div class="pt-6 bg-coolGray-100 dark:bg-gray-500 rounded-xl rounded-bl-none rounded-br-none">
        <div class="px-6">
         <div class="w-full mt-6 pb-6 overflow-x-auto">
          <table class="w-full min-w-max text-sm">
           <thead class="uppercase">
            <tr class="text-left">
             <th class="p-0">
              <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
               <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Explorer output</span>
              </div>
             </th>
            </tr>
           </thead>
           <tr class="opacity-100 text-gray-500 dark:text-gray-100">
            <td class="py-3 px-6">
             <textarea name="result" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-50 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" rows="20">{{ result }}</textarea>
            </td>
           </tr>
          </table>
         </div>
        </div>  
        </form>
       </div>
      </div>
     </div>
    </div>
 </section>
  <section>
  <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden ">
   <div class="pb-6 ">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="container mx-auto">
       <div class="pt-6 pb-6 bg-coolGray-100 dark:border-gray-400 dark:bg-gray-500 rounded-bl-xl rounded-br-xl">
        <div class="px-6">
         <div class="flex flex-wrap justify-end">
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
 </section>
{% endif %}
</div>
{% include 'footer.html' %}
</body>
</html>