{% include 'header.html' %}
{% from 'style.html' import breadcrumb_line_svg, input_arrow_down_svg %}
<div class="container mx-auto">
 <section class="p-5 mt-5">
  <div class="flex flex-wrap items-center -m-2">
   <div class="w-full md:w-1/2 p-2">
    <ul class="flex flex-wrap items-center gap-x-3 mb-2">
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/">
       <p>Home</p>
      </a>
     </li>
    <li> {{ breadcrumb_line_svg | safe }} </li>
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/settings">Settings</a>
     </li>
    <li> {{ breadcrumb_line_svg | safe }} </li>
    </ul>
   </div>
  </div>
 </section>
 <section class="py-3">
  <div class="container px-4 mx-auto">
   <div class="relative py-11 px-16 bg-coolGray-900 dark:bg-blue-500 rounded-md overflow-hidden">
    <img class="absolute z-10 left-4 top-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute z-10 right-4 bottom-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute h-64 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 object-cover" src="/static/images/elements/wave.svg" alt="">
    <div class="relative z-20 flex flex-wrap items-center -m-3">
     <div class="w-full md:w-1/2 p-3">
      <h2 class="mb-6 text-4xl font-bold text-white tracking-tighter">Settings</h2>
      <p class="font-normal text-coolGray-200 dark:text-white">Manage your BasicSwap client and coins settings.</p>
     </div>
    </div>
   </div>
  </div>
 </section>
 {% include 'inc_messages.html' %}
 <section>
  <div class="pl-6 pr-6 pt-0 pb-6 mt-5 h-full overflow-hidden">
   <div class="pb-6">
    <div class="flex flex-wrap items-center justify-between -m-2">
     <div class="w-full pt-2">
      <div class="mb-4 border-b pb-5 border-gray-200 dark:border-gray-500">
       <ul class="flex flex-wrap text-sm font-medium text-center text-gray-500 dark:text-gray-400" id="myTab" data-tabs-toggle="#settingstab" role="tablist">
        <li class="mr-2">
         <a class="inline-block px-4 py-3 rounded-lg hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" id="coins-tab" data-tabs-target="#coins" role="tab" aria-controls="coins" aria-selected={% if active_tab == 'default' %}"true"{% else %}"false"{% endif %}>Coins</a>
        </li>
        <li class="mr-2">
         <a class="inline-block px-4 py-3 rounded-lg hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" id="general-tab" data-tabs-target="#general" role="tab" aria-controls="general" aria-selected={% if active_tab == 'general' %}"true"{% else %}"false"{% endif %}>General</a>
        </li>
        <li class="mr-2">
         <a class="inline-block px-4 py-3 rounded-lg hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" id="tor-tab" data-tabs-target="#tor" role="tab" aria-controls="tor" aria-selected={% if active_tab == 'tor' %}"true"{% else %}"false"{% endif %}>Tor</a>
        </li>
       </ul>
      </div>
     </div>
    </div>
   </div>
  </div>
 </section>
 <div id="settingstab">
  <div class="hidden rounded-lg" id="coins" role="tabpanel" aria-labelledby="coins-tab">
   <form method="post">
    {% for c in chains %}
    <section class="pl-6">
     <div class="flex flex-wrap items-center">
      <div class="w-full">
       <h4 class="font-semibold text-black dark:text-white text-2xl align-middle">
        <span class="mr-2 inline-block align-middle items-center justify-center w-9 h-10 bg-white-50 rounded">
         <img class="h-9" src="/static/images/coins/{{ c.name }}.png" alt="">
        </span>
    {{ c.display_name }}
       </h4>
      </div>
     </div>
    </section>
    <section>
     <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
      <div class="border-coolGray-100">
       <div class="flex flex-wrap items-center justify-between -m-2">
        <div class="w-full pt-2">
         <div class="container mt-5 mx-auto">
          <div class="pt-6 pb-6 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
           <div class="px-6">
            <div class="w-full mt-6 pb-6 overflow-x-auto">
             <table class="w-full min-w-max text-sm">
              <thead class="uppercase">
               <tr class="text-left">
                <th class="p-0">
                 <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
                  <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Options</span>
                 </div>
                </th>
                <th class="p-0">
                 <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
                  <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Settings</span>
                 </div>
                </th>
               </tr>
              </thead>
              {% if c.connection_type %}
              <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
               <td class="py-3 px-6 bold">Connection Type</td>
               <td class="py-3 px-6">{{ c.connection_type }}</td>
              </tr>
              {% endif %}
              {% if c.manage_daemon is defined %}
              {% if c.name in ('wownero', 'monero') %}
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
               <td class="py-3 px-6 bold">Manage Daemon</td>
               <td class="py-3 px-6">
                <div class="relative">
                {{ input_arrow_down_svg| safe }}
                 <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="managedaemon_{{ c.name }}">
                  <option value="true" {% if c.manage_daemon==true %} selected{% endif %}>True</option>
                  <option value="false" {% if c.manage_daemon==false %} selected{% endif %}>False</option>
                 </select>
                </div>
               </td>
              </tr>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
               <td class="py-3 px-6 bold">Daemon RPC Host</td>
               <td class="py-3 px-6">
                <input type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="rpchost_{{ c.name }}" value="{{ c.rpchost }}">
               </td>
              </tr>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
               <td class="py-3 px-6 bold">Daemon RPC Port</td>
               <td class="py-3 px-6">
                <input type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="rpcport_{{ c.name }}" value="{{ c.rpcport }}">
               </td>
              </tr>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
               <td class="py-3 px-6 bold">List trusted public nodes to use if “Automatically Select Daemon” is set as “True”.</td>
               <td class="py-3 px-6">
                <textarea class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" placeholder="(Add one entry per line) eg: node.xmr.to:18081" name="remotedaemonurls_{{ c.name }}" rows="4" cols="45">{{ c.remotedaemonurls }}</textarea>
               </td>
              </tr>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
               <td class="py-3 px-6 bold">Automatically Select Daemon</td>
               <td class="py-3 px-6">
                <div class="w-52 md:flex-1">
                 <div class="relative">
                {{ input_arrow_down_svg| safe }}
                  <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="autosetdaemon_{{ c.name }}">
                   <option value="true" {% if c.autosetdaemon==true %} selected{% endif %}>True</option>
                   <option value="false" {% if c.autosetdaemon==false %} selected{% endif %}>False</option>
                  </select>
                 </div>
                </div>
               </td>
              </tr>
              {% else %}
              <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
               <td class="py-3 px-6 bold">Manage Daemon</td>
               <td class="py-3 px-6">{{ c.manage_daemon }}</td>
              </tr>
              {% endif %}
              {% endif %}
              {% if c.manage_wallet_daemon is defined %}
              <tr class="opacity-100 text-gray-500 dark:text-gray-100 hover:bg-coolGray-200 dark:hover:bg-gray-600">
               <td class="py-3 px-6 bold">Manage Wallet Daemon</td>
               <td class="py-3 px-6">{{ c.manage_wallet_daemon }}</td>
              </tr>
              {% endif %}
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
               <td class="py-3 px-6 bold">Chain Lookups</td>
               <td class="py-3 px-6">
                <div class="relative">
                {{ input_arrow_down_svg| safe }}
                 <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="lookups_{{ c.name }}">
                  <option value="local" {% if c.lookups=='local' %} selected{% endif %}>Local Node</option>
                  <option value="explorer" {% if c.lookups=='explorer' %} selected{% endif %}>Explorer</option>
                 </select>
                </div>
               </td>
              </tr>
              {% if c.name in ('wownero', 'monero') %}
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
               <td class="py-3 px-6 bold">Transaction Fee Priority</td>
               <td class="py-3 px-6">
                <div class="relative">
                {{ input_arrow_down_svg| safe }}
                 <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="fee_priority_{{ c.name }}">
                  <option value="0" {% if c.fee_priority==0 %} selected{% endif %}>Auto</option>
                  <option value="1" {% if c.fee_priority==1 %} selected{% endif %}>Slow</option>
                  <option value="2" {% if c.fee_priority==2 %} selected{% endif %}>Normal</option>
                  <option value="3" {% if c.fee_priority==3 %} selected{% endif %}>Fast</option>
                 </select>
                </div>
               </td>
              </tr>
              {% else %}
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
               <td class="py-3 px-6 bold">Blocks Confirmed Target</td>
               <td class="py-3 px-6">
                <input type="number" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="conf_target_{{ c.name }}" min="1" max="32" value="{{ c.conf_target }}">
               </td>
              </tr>
              {% endif %}
              {% if c.name == 'particl' %}
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
               <td class="py-3 px-6 bold">Anon Tx Ring Size</td>
               <td class="py-3 px-6">
                <input type="number" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="rct_ring_size_{{ c.name }}" min="3" max="32" value="{{ c.anon_tx_ring_size }}">
               </td>
              </tr>
              {% endif %}
             </table>
            </div>
           </div>
          </div>
         </div>
    </section>
    <section>
     <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden ">
      <div class="pb-6 ">
       <div class="flex flex-wrap items-center justify-between -m-2">
        <div class="w-full pt-2">
         <div class="container mx-auto">
          <div class="pt-6 pb-6 bg-coolGray-100 border-t border-gray-100 dark:border-gray-400 dark:bg-gray-500 rounded-bl-xl rounded-br-xl">
           <div class="px-6">
            <div class="flex flex-wrap justify-end">
             <div class="w-full md:w-auto p-1.5 ml-2">
              <button name="apply_{{ c.name }}" value="Apply" type="submit" class="flex flex-wrap justify-center w-full px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">Apply</button>
             </div>
             {% if c.can_disable == true %}
             <div class="w-full md:w-auto p-1.5 ml-2">
              <button name="disable_{{ c.name }}" value="Disable" onclick="return confirmPopup('Disable', '{{ c.display_name }}');" type="submit" class="flex flex-wrap justify-center w-full px-4 py-2.5 bg-red-500 hover:bg-red-600 font-medium text-sm text-white border border-red-500 rounded-md shadow-button focus:ring-0 focus:outline-none">Disable Coin</button>
             </div>
             {% endif %}
             {% if c.can_reenable == true %}
             <div class="w-full md:w-auto p-1.5 ml-2">
              <button name="enable_{{ c.name }}" value="Enable" onclick="return confirmPopup('Enable', '{{ c.display_name }}');" type="submit" class="flex flex-wrap justify-center w-full px-4 py-2.5 bg-green-500 hover:bg-green-600 font-medium text-sm text-white border border-green-500 rounded-md shadow-button focus:ring-0 focus:outline-none">Enable</button>
             </div>
             {% endif %}
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </section>
    {% endfor %}
    <input type="hidden" name="formid" value="{{ form_id }}">
  </div>
  <div class="hidden rounded-lg" id="general" role="tabpanel" aria-labelledby="general-tab">
   <section class="pl-6">
    <div class="flex flex-wrap items-center">
     <div class="w-full">
      <h4 class="font-semibold text-black dark:text-white text-2xl align-middle">
       <span class="mr-2 inline-block align-middle items-center justify-center w-0 h-10 bg-white-50 rounded">
       </span>General
      </h4>
     </div>
    </div>
   </section>
   <section>
    <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
     <div class="border-coolGray-100">
      <div class="flex flex-wrap items-center justify-between -m-2">
       <div class="w-full pt-2">
        <div class="container mt-5 mx-auto">
         <div class="pt-6 pb-6 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
          <div class="px-6">
           <div class="w-full mt-6 pb-6 overflow-x-auto">
            <table class="w-full min-w-max text-sm">
             <thead class="uppercase">
              <tr class="text-left">
               <th class="p-0">
                <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
                 <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Options</span>
                </div>
               </th>
               <th class="p-0">
                <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
                 <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Settings</span>
                </div>
               </th>
              </tr>
             </thead>
             <tr>
            <td colspan="2" class="py-3 px-6">
              <div class="flex items-center">
                <span class="text-red-500 dark:text-red-500 text-sm font-medium">WARNING: Advanced features - Only enable if you know what you're doing!</span>
             </div>
             </td>
             </tr>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
              <td class="py-3 px-6 bold w-96 bold">Debug Mode</td>
              <td class="py-3 px-6">
               <div class="relative">
                {{ input_arrow_down_svg| safe }}
                <select name="debugmode" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0">
                 <option {% if general_settings.debug %}selected{% endif %} value="true">True</option>
                 <option {% if not general_settings.debug %}selected{% endif %} value="false">False</option>
                </select>
               </div>
              </td>
             </tr>
             <tr class="opacity-100 text-gray-500 dark:text-gray-100">
              <td class="py-3 px-6 bold">Debug Mode UI</td>
              <td class="py-3 px-6">
               <div class="relative">
                {{ input_arrow_down_svg| safe }}
                <select name="debugui" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0">
                 <option {% if general_settings.debug_ui %}selected{% endif %} value="true">True</option>
                 <option {% if not general_settings.debug_ui %}selected{% endif %} value="false">False</option>
                </select>
               </div>
              </td>
             </tr>
             <tr class="opacity-100 text-gray-500 dark:text-gray-100">
              <td class="py-3 px-6 bold">Remove DB records for expired offers</td>
              <td class="py-3 px-6">
               <div class="relative">
                {{ input_arrow_down_svg| safe }}
                <select name="expire_db_records" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0">
                 <option {% if general_settings.expire_db_records %}selected{% endif %} value="true">True</option>
                 <option {% if not general_settings.expire_db_records %}selected{% endif %} value="false">False</option>
                </select>
               </div>
              </td>
             </tr>
            </table>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </section>
   <section>
    <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden ">
     <div class="pb-6 ">
      <div class="flex flex-wrap items-center justify-between -m-2">
       <div class="w-full pt-2">
        <div class="container mx-auto">
         <div class="pt-6 pb-6 bg-coolGray-100 border-t border-gray-100 dark:border-gray-400 dark:bg-gray-500 rounded-bl-xl rounded-br-xl">
          <div class="px-6">
           <div class="flex flex-wrap justify-end">
            <div class="w-full md:w-auto p-1.5 ml-2">
             <button name="apply_general" value="Apply" type="submit" class="flex flex-wrap justify-center w-full px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">Apply</button>
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </section>
   <section class="pl-6">
    <div class="flex flex-wrap items-center">
     <div class="w-full">
      <h4 class="font-semibold text-black dark:text-white text-2xl align-middle">
       <span class="mr-2 inline-block align-middle items-center justify-center w-0 h-10 bg-white-50 rounded">
       </span>Price Chart
      </h4>
     </div>
    </div>
   </section>
   <section>
    <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
     <div class="border-coolGray-100">
      <div class="flex flex-wrap items-center justify-between -m-2">
       <div class="w-full pt-2">
        <div class="container mt-5 mx-auto">
         <div class="pt-6 pb-6 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
          <div class="px-6">
           <div class="w-full mt-6 pb-6 overflow-x-auto">
            <table class="w-full min-w-max text-sm">
             <thead class="uppercase">
              <tr class="text-left">
               <th class="p-0">
                <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
                 <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Options</span>
                </div>
               </th>
               <th class="p-0">
                <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
                 <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Settings</span>
                </div>
               </th>
              </tr>
             </thead>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
              <td class="py-3 px-6 bold w-96 bold">Show Price Chart</td>
              <td class="py-3 px-6">
               <div class="relative">
                {{ input_arrow_down_svg| safe }}
                <select name="showchart" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0">
                 <option {% if chart_settings.show_chart %}selected{% endif %} value="true">True</option>
                 <option {% if not chart_settings.show_chart %}selected{% endif %} value="false">False</option>
                </select>
               </div>
              </td>
             </tr>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
              <td class="py-3 px-6 bold">Chart API Key (CryptoCompare)</td>
              <td class="py-3 px-6">
               <label for="chartapikey" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Chart API (free) Key at <a class="inline-block text-blue-500 hover:text-blue-600 hover:underline" href="https://min-api.cryptocompare.com/" target="_blank">CryptoCompare.com</a>
                <br />
               </label>
               <input name="chartapikey" type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" min="3" max="32" value="{{chart_settings.chart_api_key}}">
              </td>
             </tr>
             <tr class="opacity-100 text-gray-500 dark:text-gray-100">
             <td class="py-3 px-6 bold">Chart API Key (CoinGecko)</td>
             <td class="py-3 px-6">
              <label for="coingeckoapikey" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Chart API (free) Key at <a class="inline-block text-blue-500 hover:text-blue-600 hover:underline" href="https://coingecko.com/" target="_blank">CoinGecko.com</a>
               <br />
              </label>
              <input name="coingeckoapikey" type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" min="3" max="32" value="{{chart_settings.coingecko_api_key}}">
             </td>
            </tr>
            <tr class="opacity-100 text-gray-500 dark:text-gray-100">
             <td class="py-3 px-6 bold">Enabled Coins</td>
             <td class="py-3 px-6">
              <label for="enabledchartcoins" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Coins to show data for: Blank for active coins, "all" for all known coins or comma separated<br/> list of coin tickers to show
               <br />
              </label>
              <input name="enabledchartcoins" type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" value="{{chart_settings.enabled_chart_coins}}">
             </td>
            </tr>
            </table>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </section>
   <section>
    <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden ">
     <div class="pb-6 ">
      <div class="flex flex-wrap items-center justify-between -m-2">
       <div class="w-full pt-2">
        <div class="container mx-auto">
         <div class="pt-6 pb-6 bg-coolGray-100 border-t border-gray-100 dark:border-gray-400 dark:bg-gray-500 rounded-bl-xl rounded-br-xl">
          <div class="px-6">
           <div class="flex flex-wrap justify-end">
            <div class="w-full md:w-auto p-1.5 ml-2">
             <button name="apply_chart" value="Apply" type="submit" class="flex flex-wrap justify-center w-full px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">Apply</button>
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </section>
  </div>
  <div class="hidden rounded-lg" id="tor" role="tabpanel" aria-labelledby="tor-tab">
   <section class="pl-6">
    <div class="flex flex-wrap items-center">
     <div class="w-full">
      <h4 class="font-semibold text-black dark:text-white text-2xl align-middle">
       <span class="mr-2 inline-block align-middle items-center justify-center w-0 h-10 bg-white-50 rounded">
       </span>Tor
      </h4>
     </div>
    </div>
   </section>
   <section>
    <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
     <div class="border-coolGray-100">
      <div class="flex flex-wrap items-center justify-between -m-2">
       <div class="w-full pt-2">
        <div class="container mt-5 mx-auto">
         <div class="pt-6 pb-6 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
          <div class="px-6">
           <div class="w-full mt-6 pb-6 overflow-x-auto">
            <table class="w-full min-w-max text-sm">
             <thead class="uppercase">
              <tr class="text-left">
               <th class="p-0">
                <div class="py-3 px-6 rounded-tl-xl bg-coolGray-200 dark:bg-gray-600">
                 <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Options</span>
                </div>
               </th>
               <th class="p-0">
                <div class="py-3 px-6 rounded-tr-xl bg-coolGray-200 dark:bg-gray-600">
                 <span class="text-xs text-gray-600 dark:text-gray-300 font-semibold">Settings</span>
                </div>
               </th>
              </tr>
             </thead>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
              <td class="py-3 px-6 bold">Use Tor Proxy</td>
              <td class="py-3 px-6">
               <div class="w-1/5 md:flex-1">
                <div class="relative">
                {{ input_arrow_down_svg| safe }}
                 <select name="usetorproxy" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0">
                  <option {% if tor_settings.use_tor %}selected{% endif %} value="true">True</option>
                  <option {% if not tor_settings.use_tor %}selected{% endif %} value="false">False</option>
                 </select>
                </div>
               </div>
              </td>
             </tr>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
              <td class="py-3 px-6 bold w-96">Tor Proxy Host</td>
              <td class="py-3 px-6">
               <input name="proxyhost" type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="" min="3" max="32" value="{{tor_settings.proxy_host}}">
              </td>
             </tr>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
              <td class="py-3 px-6 bold w-96">Tor Proxy Port</td>
              <td class="py-3 px-6">
               <input name="proxyport" type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="" min="3" max="32" value="{{tor_settings.proxy_port}}">
              </td>
             </tr>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
              <td class="py-3 px-6 bold w-96">Tor Control Port</td>
              <td class="py-3 px-6">
               <input name="controlport" type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="" min="3" max="32" value="{{tor_settings.control_port}}">
              </td>
             </tr>
              <tr class="opacity-100 text-gray-500 dark:text-gray-100">
              <td class="py-3 px-6 bold w-96">Tor Control Password</td>
              <td class="py-3 px-6">
               <input name="controlpwd" type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="" min="3" max="32" value="{{tor_settings.control_password}}">
              </td>
             </tr>
            </table>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </section>
   <section>
    <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden ">
     <div class="pb-6 ">
      <div class="flex flex-wrap items-center justify-between -m-2">
       <div class="w-full pt-2">
        <div class="container mx-auto">
         <div class="pt-6 pb-6 bg-coolGray-100 border-t border-gray-100 dark:border-gray-400 dark:bg-gray-500 rounded-bl-xl rounded-br-xl">
          <div class="px-6">
           <div class="flex flex-wrap justify-end">
            <div class="w-full md:w-auto p-1.5 ml-2">
             <button name="apply_tor" value="Apply" type="submit" class="flex flex-wrap justify-center w-full px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">Apply</button>
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
   </section>
  </div>
 </div>
  </div>
<script>
  function confirmPopup(action, coin_name) {
   return confirm(action + " " + coin_name + "?\nWill shutdown basicswap.");
  }
</script>
{% include 'footer.html' %}
</body>
</html>
